import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { supabaseAdmin } from '@/lib/supabase-admin';
import type { Character } from '@/types/schema.types';

/**
 * GET /api/v1/characters/[characterId]
 * Get character details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    // Only allow authenticated users
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { characterId } = await params;

    // Get the character, ensuring world_id is selected for authorization check
    const { data: character, error } = await supabaseAdmin
      .from('characters')
      .select('*, world_id') // Explicitly select world_id
      .eq('id', characterId)
      .single();

    if (error) {
      console.error("Error fetching character:", error);
      if (error.code === 'PGRST116') { // PostgREST code for no rows found
        return NextResponse.json({ error: 'Character not found' }, { status: 404 });
      }
      return NextResponse.json({ error: `Character fetch error: ${error.message}` }, { status: 500 });
    }

    if (!character.world_id) {
        console.error(`Character ${characterId} is missing world_id.`);
        return NextResponse.json({ error: 'Character data incomplete (missing world link)' }, { status: 500 });
    }

    // Fetch the world the character belongs to, to check its owner
    const { data: world, error: worldError } = await supabaseAdmin
      .from('worlds')
      .select('user_id') // Select the owner of the world
      .eq('id', character.world_id)
      .single();

    if (worldError) {
        console.error(`Error fetching world ${character.world_id} for character auth:`, worldError);
        // Handle world fetch error (e.g., world not found, though unlikely if character exists)
        return NextResponse.json({ error: 'Failed to verify world access' }, { status: 500 });
    }

    // Fetch requesting user's admin status
    const { data: requestingUserData, error: userError } = await supabaseAdmin
      .from('users')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (userError) {
        console.error('Error fetching user data for character auth:', userError);
        return NextResponse.json({ error: 'Failed to verify user permissions' }, { status: 500 });
    }

    // --- Authorization Check ---
    // Allow access if the requesting user is an admin OR owns the world this character belongs to.
    const isAdmin = requestingUserData?.is_admin ?? false;
    const isWorldOwner = world.user_id === session.user.id;

    if (!isAdmin && !isWorldOwner) {
      console.warn(`Forbidden access attempt: User ${session.user.id} tried to access character ${characterId} in world ${character.world_id} owned by ${world.user_id}`);
      return NextResponse.json({ error: 'Forbidden: You do not have access to this character via world ownership.' }, { status: 403 });
    }

    // User is authorized, return character data
    // Optionally remove world_id if not needed by frontend display
    // delete character.world_id; 
    
    return NextResponse.json(character as Character);
  } catch (error) {
    console.error('Error fetching character:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/characters/[characterId]
 * Update character details
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    // Only allow authenticated users
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { characterId } = params;
    const updates = await request.json();

    // Get the character to check ownership
    const { data: character, error: fetchError } = await supabaseAdmin
      .from('characters')
      .select('user_id')
      .eq('id', characterId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Character not found' }, { status: 404 });
      }
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    // Check if user owns this character or is admin
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData?.is_admin && character.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Prevent updating user_id
    if (updates.user_id) {
      delete updates.user_id;
    }

    // Add updated_at timestamp
    updates.updated_at = new Date().toISOString();

    // Update the character
    const { data: updatedCharacter, error: updateError } = await supabaseAdmin
      .from('characters')
      .update(updates)
      .eq('id', characterId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json(updatedCharacter as Character);
  } catch (error) {
    console.error('Error updating character:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/characters/[characterId]
 * Delete character
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    // Only allow authenticated users
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { characterId } = params;

    // Get the character to check ownership
    const { data: character, error: fetchError } = await supabaseAdmin
      .from('characters')
      .select('user_id')
      .eq('id', characterId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Character not found' }, { status: 404 });
      }
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    // Check if user owns this character or is admin
    // admin check here
    const { data: userData, error: userError } = await supabaseAdmin
    .from('users')
    .select('is_admin')
    .eq('id', session.user.id)
    .single();

    if (userError || !userData?.is_admin && character.user_id !== session.user.id) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }



    // Delete the character
    const { error: deleteError } = await supabaseAdmin
      .from('characters')
      .delete()
      .eq('id', characterId);

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Character deleted successfully' });
  } catch (error) {
    console.error('Error deleting character:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
