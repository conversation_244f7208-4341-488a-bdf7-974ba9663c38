import { NextResponse } from 'next/server';

/**
 * Returns client-exposed environment variables (NEXT_PUBLIC_*)
 * without revealing sensitive values.
 */
export async function GET() {
  const envVars: Record<string, string> = {};

  Object.keys(process.env).forEach((key) => {
    if (key.startsWith('NEXT_PUBLIC_')) {
      envVars[key] = process.env[key] ? 'Set' : 'Not Set';
    }
  });

  return NextResponse.json(envVars);
}
