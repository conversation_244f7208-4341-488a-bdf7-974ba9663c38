import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import jwt from 'jsonwebtoken';

/**
 * API endpoint to refresh the Supabase JWT token
 * This is called by the client when the token is about to expire
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the JWT secret
    const supabaseJwtSecret = process.env.SUPABASE_JWT_SECRET;
    if (!supabaseJwtSecret) {
      console.error('[RefreshToken] SUPABASE_JWT_SECRET not set');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Create a new JWT payload
    const payload = {
      aud: "authenticated",
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour expiry
      sub: session.user.id, // The user ID from our database
      email: session.user.email,
      role: "authenticated",
      user_id: session.user.id, // Duplicate the user ID in a field that might be more accessible
      iat: Math.floor(Date.now() / 1000), // Issued at time
      jti: Math.random().toString(36).substring(2) // Unique token ID
    };

    // Sign the JWT with the Supabase JWT secret
    const newToken = jwt.sign(payload, supabaseJwtSecret);

    // Return the new token
    return NextResponse.json({ token: newToken });
  } catch (error) {
    console.error('[RefreshToken] Error refreshing token:', error);
    return NextResponse.json({ error: 'Failed to refresh token' }, { status: 500 });
  }
}
