import { NextResponse } from 'next/server';

// Debug endpoint to check environment variables on the server
export async function GET() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  return NextResponse.json({
    // Check if values are present, don't return actual values for security
    supabaseUrl: {
      set: Boolean(supabaseUrl),
      length: supabaseUrl?.length || 0,
      firstChars: supabaseUrl?.substring(0, 8) || '',
      lastChars: supabaseUrl?.substring(supabaseUrl.length - 3) || '',
    },
    supabaseServiceKey: {
      set: <PERSON><PERSON><PERSON>(supabaseServiceKey),
      length: supabaseServiceKey?.length || 0,
      firstChars: supabaseServiceKey?.substring(0, 8) || '',
      lastChars: supabaseServiceKey?.substring(supabaseServiceKey?.length - 3) || '',
    },
    nextAuthUrl: {
      set: <PERSON><PERSON><PERSON>(process.env.NEXTAUTH_URL),
      value: process.env.NEXTAUTH_URL,
    },
    nextAuthSecret: {
      set: Boolean(process.env.NEXTAUTH_SECRET),
      length: process.env.NEXTAUTH_SECRET?.length || 0,
    },
    // Check for potential environment loading issues
    nodeEnv: process.env.NODE_ENV,
    processType: typeof process.env,
    envKeys: Object.keys(process.env).filter(key => 
      key.includes('SUPABASE') || key.includes('NEXT') || key.includes('AUTH')
    ),
  });
} 