import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import type { Character } from '@/types/schema.types';

/**
 * GET /api/v1/users/[userId]/characters
 * Also handles /api/v1/users/me/characters when [userId] is 'me'
 * Get all characters for a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = (await params).userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Query for URL parameters
    const searchParams = request.nextUrl.searchParams;
    const worldId = searchParams.get('world_id');

    // Build query
    let query = supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact' })
      .eq('user_id', targetUserId)
      .order('created_at', { ascending: false });

    // Add filters if provided
    if (worldId) {
      query = query.eq('world_id', worldId);
    }

    // Get all characters for the user
    const { data: characters, error, count } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform the data to match frontend expectations
    const transformedCharacters = (characters as Character[]).map(character => ({
      id: character.id,
      name: character.name,
      race: character.race || '',
      class: character.class || '',
      attributes: character.attributes || {},
      background: character.background || '',
      alignment: character.alignment || '',
      gender: character.gender || '',
      description: character.description || '',
      type: character.type || 'player',
      inventory: character.inventory || '',
      memories: character.memories || '',
      createdAt: character.created_at,
      updatedAt: character.updated_at
    }));
    // console.log('Characters:', transformedCharacters);

    return NextResponse.json({
      characters: transformedCharacters,
      count,
    });
  } catch (error) {
    console.error('Error fetching characters:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/users/[userId]/characters
 * Also handles /api/v1/users/me/characters when [userId] is 'me'
 * Create a new character for a specific user
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = (await params).userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Parse request body
    const characterData = await request.json();

    // Validate required fields
    if (!characterData.name) {
      return NextResponse.json(
        { error: 'Missing required field: name' },
        { status: 400 }
      );
    }

    // Create the character in Supabase
    const { data: newCharacter, error } = await supabaseAdmin
      .from('characters')
      .insert({
        name: characterData.name,
        race: characterData.race || '',
        class: characterData.class || '',
        background: characterData.background || '',
        alignment: characterData.alignment || '',
        gender: characterData.gender || '',
        type: characterData.type || 'player',
        description: characterData.description || '',
        inventory: characterData.inventory || '',
        memories: characterData.memories || '',
        attributes: {
          level: characterData.level || 1,
          ...characterData.attributes
        },
        user_id: targetUserId,
        world_id: characterData.world_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return the new character
    const c = newCharacter as Character;
    return NextResponse.json({
      id: c.id,
      name: c.name,
      race: c.race,
      class: c.class,
      level: c.attributes.level,
      background: c.background,
      alignment: c.alignment,
      gender: c.gender,
      description: c.description,
      type: c.type,
      inventory: c.inventory,
      memories: c.memories,
      createdAt: c.created_at,
      updatedAt: c.updated_at
    });
  } catch (error) {
    console.error('Error creating character:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
