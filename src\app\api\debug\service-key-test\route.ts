import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/lib/env';

export async function GET() {
  const results: Record<string, any> = {
    envStatus: {
      url: <PERSON><PERSON><PERSON>(env.SUPABASE_URL),
      serviceKey: <PERSON><PERSON><PERSON>(env.SUPABASE_SERVICE_KEY)
    },
    tests: []
  };
  
  // Test with direct instantiation to check for any issues
  try {
    if (!env.SUPABASE_URL || !env.SUPABASE_SERVICE_KEY) {
      throw new Error('Missing required environment variables');
    }
    
    // Try creating a client without the library-level instance
    const testClient = createClient(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    results.tests.push({
      name: 'Client creation',
      success: true,
      message: 'Supabase client created successfully'
    });
    
    // Try to perform a simple query
    try {
      const { data, error, status } = await testClient
        .from('users')
        .select('count(*)', { count: 'exact', head: true });
      
      if (error) {
        results.tests.push({
          name: 'Simple query',
          success: false,
          message: `Error performing query: ${error.message}`,
          status,
          error
        });
      } else {
        results.tests.push({
          name: 'Simple query',
          success: true,
          message: 'Query executed successfully',
          status
        });
      }
    } catch (queryError: any) {
      results.tests.push({
        name: 'Simple query',
        success: false,
        message: `Exception during query: ${queryError.message}`,
        error: queryError.toString()
      });
    }
    
    // Try to perform an RLS-protected operation (requires service role)
    try {
      // Create a test record
      const testId = `test-${Date.now()}`;
      const { error: insertError } = await testClient
        .from('users')
        .insert({
          id: testId,
          email: `test-${Date.now()}@example.com`,
          name: 'Service Key Test',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      // Immediately delete it to clean up
      if (!insertError) {
        await testClient
          .from('users')
          .delete()
          .eq('id', testId);
      }
      
      results.tests.push({
        name: 'RLS bypass operation',
        success: !insertError,
        message: insertError 
          ? `Failed to bypass RLS: ${insertError.message}` 
          : 'Successfully bypassed RLS with service role key',
        error: insertError || null
      });
    } catch (rlsError: any) {
      results.tests.push({
        name: 'RLS bypass operation',
        success: false,
        message: `Exception during RLS test: ${rlsError.message}`,
        error: rlsError.toString()
      });
    }
    
  } catch (e: any) {
    results.tests.push({
      name: 'Client creation',
      success: false,
      message: `Failed to create client: ${e.message}`,
      error: e.toString()
    });
  }
  
  const allSuccessful = results.tests.every((test: any) => test.success);
  
  return NextResponse.json({
    success: allSuccessful,
    message: allSuccessful 
      ? 'All service key tests passed' 
      : 'One or more service key tests failed',
    results
  });
} 