'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { createCharacter } from '@/lib/character-service'
import { getWorldsByUserId } from '@/lib/world-service'

type CharacterClass = 'fighter' | 'wizard' | 'rogue' | 'cleric' | 'bard' | 'druid'
type CharacterRace = 'human' | 'elf' | 'dwarf' | 'halfling' | 'tiefling' | 'dragonborn'

interface World {
  id: string;
  name: string;
}

export default function CreateCharacterPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [worlds, setWorlds] = useState<World[]>([])
  
  // Form state
  const [name, setName] = useState('')
  const [race, setRace] = useState<CharacterRace>('human')
  const [characterClass, setCharacterClass] = useState<CharacterClass>('fighter')
  const [level, setLevel] = useState(1)
  const [worldId, setWorldId] = useState<string | null>(null)
  
  // Available options
  const races: CharacterRace[] = ['human', 'elf', 'dwarf', 'halfling', 'tiefling', 'dragonborn']
  const classes: CharacterClass[] = ['fighter', 'wizard', 'rogue', 'cleric', 'bard', 'druid']
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signup')
    } else if (status === 'authenticated') {
      setIsLoading(false)
      loadWorlds()
    }
  }, [status, router])
  
  // Load user's worlds from Supabase
  const loadWorlds = async () => {
    if (!session?.user?.id) return
    
    try {
      const fetchedWorlds = await getWorldsByUserId(session.user.id)
      setWorlds(fetchedWorlds)
      
      // Set default world if available
      if (fetchedWorlds.length > 0) {
        setWorldId(fetchedWorlds[0].id)
      }
    } catch (err) {
      console.error('Error loading worlds:', err)
      setError('Failed to load worlds. Please try again later.')
    }
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session?.user?.id) {
      setError('You must be logged in to create a character')
      return
    }
    
    if (!name.trim()) {
      setError('Character name is required')
      return
    }
    
    try {
      setIsSaving(true)
      setError(null)
      
      const newCharacter = await createCharacter(
        session.user.id,
        name,
        race,
        characterClass,
        level,
        worldId,
        {},  // Default empty attributes
        'player'  // Default to player type
      )
      
      if (newCharacter) {
        router.push('/profile?tab=characters')
      } else {
        setError('Failed to create character. Please try again.')
      }
    } catch (err) {
      console.error('Error creating character:', err)
      setError('An error occurred while creating the character.')
    } finally {
      setIsSaving(false)
    }
  }
  
  if (isLoading) {
    return (
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto bg-background-darker border border-gray-800 rounded-lg p-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-800 rounded w-3/4 mx-auto"></div>
            <div className="h-4 bg-gray-800 rounded w-1/2 mx-auto"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-800 rounded"></div>
              <div className="h-10 bg-gray-800 rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-800 rounded"></div>
              <div className="h-10 bg-gray-800 rounded"></div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-800 rounded"></div>
              <div className="h-10 bg-gray-800 rounded"></div>
            </div>
            <div className="h-10 bg-gray-800 rounded w-1/3 mx-auto"></div>
          </div>
        </div>
      </main>
    )
  }
  
  return (
    <main className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto bg-background-darker border border-gray-800 rounded-lg overflow-hidden shadow-xl">
        <div className="bg-gradient-to-r from-background-darker to-primary/20 p-6 text-center">
          <h1 className="text-2xl font-medieval text-primary">Create New Character</h1>
          <p className="text-text-secondary mt-2">Forge your hero's identity</p>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="p-4 bg-red-900/20 border border-red-900 rounded-lg text-red-400 text-sm">
              {error}
            </div>
          )}
          
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-text-secondary text-sm mb-1">
                Character Name <span className="text-accent">*</span>
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter character name"
                className="w-full p-3 bg-background border border-gray-800 rounded-md focus:border-primary focus:ring focus:ring-primary/20 focus:outline-none"
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="race" className="block text-text-secondary text-sm mb-1">
                  Race <span className="text-accent">*</span>
                </label>
                <select
                  id="race"
                  value={race}
                  onChange={(e) => setRace(e.target.value as CharacterRace)}
                  className="w-full p-3 bg-background border border-gray-800 rounded-md focus:border-primary focus:ring focus:ring-primary/20 focus:outline-none"
                  required
                >
                  {races.map((raceOption) => (
                    <option key={raceOption} value={raceOption}>
                      {raceOption.charAt(0).toUpperCase() + raceOption.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="class" className="block text-text-secondary text-sm mb-1">
                  Class <span className="text-accent">*</span>
                </label>
                <select
                  id="class"
                  value={characterClass}
                  onChange={(e) => setCharacterClass(e.target.value as CharacterClass)}
                  className="w-full p-3 bg-background border border-gray-800 rounded-md focus:border-primary focus:ring focus:ring-primary/20 focus:outline-none"
                  required
                >
                  {classes.map((classOption) => (
                    <option key={classOption} value={classOption}>
                      {classOption.charAt(0).toUpperCase() + classOption.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="level" className="block text-text-secondary text-sm mb-1">
                  Level <span className="text-accent">*</span>
                </label>
                <input
                  id="level"
                  type="number"
                  min="1"
                  max="20"
                  value={level}
                  onChange={(e) => setLevel(parseInt(e.target.value))}
                  className="w-full p-3 bg-background border border-gray-800 rounded-md focus:border-primary focus:ring focus:ring-primary/20 focus:outline-none"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="world" className="block text-text-secondary text-sm mb-1">
                  World
                </label>
                <select
                  id="world"
                  value={worldId || ''}
                  onChange={(e) => setWorldId(e.target.value || null)}
                  className="w-full p-3 bg-background border border-gray-800 rounded-md focus:border-primary focus:ring focus:ring-primary/20 focus:outline-none"
                >
                  <option value="">No World (Independent Character)</option>
                  {worlds.map((world) => (
                    <option key={world.id} value={world.id}>
                      {world.name}
                    </option>
                  ))}
                </select>
                {worlds.length === 0 && (
                  <p className="mt-1 text-xs text-text-secondary">
                    <Link href="/worlds" className="text-primary hover:underline">
                      Create a world
                    </Link>{' '}
                    to assign this character to a campaign.
                  </p>
                )}
              </div>
            </div>
          </div>
          
          <div className="pt-4 border-t border-gray-800 flex justify-end space-x-4">
            <Link
              href="/profile?tab=characters"
              className="py-2 px-4 border border-gray-800 rounded-md text-text-secondary hover:text-white transition-colors"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSaving}
              className="fantasy-button"
            >
              {isSaving ? 'Creating...' : 'Create Character'}
            </button>
          </div>
        </form>
      </div>
    </main>
  )
} 