'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { useEffect } from 'react'

const scrollToFeatures = (e: React.MouseEvent<HTMLAnchorElement>) => {
  e.preventDefault()
  document.querySelector('#features')?.scrollIntoView({ behavior: 'smooth' })
}

export default function Home() {
  return (
    <div className="flex flex-col items-center">
      {/* Hero Section */}
      <motion.section 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="w-full py-12 md:py-24 text-center"
      >
        <motion.h1 
          className="text-4xl md:text-6xl font-bold text-primary mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          Embark on a <span className="text-secondary">Solo Adventure</span>
        </motion.h1>
        <motion.p 
          className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto medieval-text"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          An AI-powered Dungeons & Dragons experience for single players
        </motion.p>
        <motion.div 
          className="flex flex-wrap justify-center gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link href="/worlds" className="fantasy-button text-lg">
              Begin Your Journey
            </Link>
          </motion.div>
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link href="#features" onClick={scrollToFeatures} className="fantasy-button bg-background hover:bg-background-darker text-lg">
              Learn More
            </Link>
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Features Section */}
      <section id="features" className="w-full py-16 scroll-parchment my-8">
        <motion.h2 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold text-center mb-12"
        >
          <span className="medieval-text text-secondary">Magical</span> Features
        </motion.h2>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {[
            {
              title: "AI Companions",
              description: "Join forces with AI-controlled party members who adapt to your play style and story decisions.",
              icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              )
            },
            {
              title: "Virtual Dice Rolls",
              description: "Experience the thrill of dice rolls with beautifully animated virtual dice that determine your fate.",
              icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              )
            },
            {
              title: "Immersive Storytelling",
              description: "Dive into rich narratives that respond to your choices and create unique adventures every time.",
              icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              )
            }
          ].map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
            >
              <FeatureCard {...feature} />
            </motion.div>
          ))}
        </div>
      </section>

      {/* How It Works Section */}
      <section className="w-full py-16">
        <motion.h2 
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-3xl md:text-4xl font-bold text-center mb-12"
        >
          How It <span className="text-primary">Works</span>
        </motion.h2>
        
        <div className="max-w-4xl mx-auto space-y-8">
          {[
            {
              number: 1,
              title: "Create Your World",
              description: "Use our intuitive world creation tools to design your campaign setting, complete with unique regions, NPCs, and quests."
            },
            {
              number: 2,
              title: "Form Your Party",
              description: "Recruit AI-powered companions who'll join you on your adventure, each with their own personality and skills."
            },
            {
              number: 3,
              title: "Embark on Adventures",
              description: "Engage in immersive storytelling where your decisions shape the narrative and determine the outcome of your journey."
            }
          ].map((step, index) => (
            <motion.div
              key={step.number}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
            >
              <Step {...step} />
            </motion.div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <motion.section 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        className="w-full py-16 bg-gradient-to-r from-background-darker to-background rounded-lg my-8 border border-gray-800"
      >
        <div className="text-center max-w-3xl mx-auto px-4">
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-6"
          >
            Ready to Begin Your <span className="text-secondary">Quest</span>?
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-lg mb-8"
          >
            Sign up now and start crafting your own unique adventures in a world limited only by your imagination.
          </motion.p>
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link href="/signup" className="fantasy-button text-lg px-8 py-3">
              Create Your Account
            </Link>
          </motion.div>
        </div>
      </motion.section>
    </div>
  )
}

function FeatureCard({ title, description, icon }: { title: string; description: string; icon: React.ReactNode }) {
  return (
    <motion.div 
      className="fantasy-card p-6 flex flex-col items-center text-center"
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <motion.div 
        className="mb-4"
        animate={{ y: [0, -5, 0] }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
      >
        {icon}
      </motion.div>
      <h3 className="text-xl font-bold mb-2 text-primary">{title}</h3>
      <p className="text-text-secondary">{description}</p>
    </motion.div>
  )
}

function Step({ number, title, description }: { number: number; title: string; description: string }) {
  return (
    <div className="flex items-start gap-4">
      <motion.div 
        className="flex-shrink-0 w-12 h-12 rounded-full bg-primary flex items-center justify-center font-bold text-xl"
        whileHover={{ scale: 1.1 }}
        transition={{ type: "spring", stiffness: 400 }}
      >
        {number}
      </motion.div>
      <div>
        <h3 className="text-xl font-bold mb-2">{title}</h3>
        <p className="text-text-secondary">{description}</p>
      </div>
    </div>
  )
} 