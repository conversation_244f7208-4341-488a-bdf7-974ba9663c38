# System Patterns: SoloQuest

## Architecture Overview

The application follows a **full-stack web architecture** built using the **Next.js framework**.

*   **Frontend:** React components rendered server-side (SSR) by default, with client-side rendering (CSR) enabled via `'use client'` directive for interactive pages/components (e.g., `src/app/page.tsx`). Uses Next.js App Router (`src/app/`).
    *   Session management is handled client-side using `useSession` from `next-auth/react`, enabling conditional rendering of UI elements based on authentication state.
    *   Status indicators (e.g., Auth, DB) are displayed in the UI using `src/app/components/Header`, with DB status checked via fetch to an internal API route.
    *   Responsive navigation is implemented, with desktop and mobile layouts, profile dropdown, and sign out functionality.
    *   Theming and custom classes (e.g., fantasy-button, text-primary) are used for a consistent fantasy look and feel.
    *   Navigation links are highlighted based on the current route using `usePathname`.
    *   Page-level data fetching is performed using custom hooks (e.g., `useUserWorlds`), with optimistic UI updates via mutate.
    *   Form state and logic are managed via custom hooks (e.g., `useWorldForm`), with form submission handled client-side and POST requests to internal API routes.
    *   Conditional rendering is used for loading, authenticated, and unauthenticated states.
    *   Themed UI components (e.g., `WorldCreationForm`, `WorldsList`, `fantasy-card`, `scroll-parchment`) are used for a cohesive user experience.
    *   User guidance is provided via contextual tips and instructions within the UI.

*   **Backend:** API routes defined within the Next.js application (`src/app/api/`).
    *   API routes follow RESTful conventions, exporting async functions for HTTP verbs (e.g., `GET`, `POST`).
    *   Authentication and authorization are enforced using `getServerSession` and checks against Supabase data (e.g., admin status, resource ownership).
        *   **World API (`GET /api/v1/worlds/[worldId]`):** After authorizing the user (owner or admin), fetches world data *and* embeds associated characters using `select('*, characters(*)')`.
        *   **Character API Auth (`GET /api/v1/characters/[characterId]`):** Access is granted if the requesting user is an admin OR owns the world the character belongs to (verified via `character.world_id` -> `world.user_id`).
    *   Data access in API routes is performed using the `supabaseAdmin` client, which bypasses RLS for privileged operations.
    *   Sensitive data is redacted before returning responses.
    *   Error handling is implemented with appropriate HTTP status codes and messages.
*   **UI Animation:** `framer-motion` library is used for UI animations (seen in `src/app/page.tsx`).
*   **Database:** Supabase (PostgreSQL) is used for data persistence, inferred from `supabase/schema.sql` and `lib/supabase-admin.ts`.
    *   Core tables: users, worlds, characters, campaigns, messages, n8n_chat_histories.
    *   All major entities use UUID primary keys (uuid_generate_v4()).
    *   Foreign key relationships enforce referential integrity (e.g., worlds → users, characters → users/worlds, campaigns → worlds, messages → users/worlds/characters).
    *   JSONB columns are used for flexible data storage (e.g., settings, attributes, message).
    *   Indexes are created for performance on foreign keys, created_at, and email.
    *   Some tables (e.g., campaign_participants) are commented out, indicating planned extensibility.
*   **Authentication:** NextAuth.js is used for backend setup (`src/app/api/auth/[...nextauth]/`) and `next-auth/react` (`useSession`, `signOut`) is used heavily in client components (`Header.tsx`) for managing session state, displaying user info, and conditional rendering of UI elements (login/logout buttons, profile dropdown).
    *   Google OAuth is configured as the authentication provider.
    *   Custom sign-in page is set to `/signup`.
    *   JWT and session callbacks sync user data with the Supabase users table (insert/update as needed) and store the Supabase UUID in the token/session.
    *   Duplicate user/email edge cases are handled robustly.
    *   Debug logging and error handling are implemented for authentication flows.
    *   The handler is exported for both GET and POST requests.
*   **Global Providers:** A `Providers` component (`src/components/Providers.tsx`) wraps the main layout. Its primary confirmed role is to provide the NextAuth `SessionProvider`, enabling client-side session access via `useSession`. The pattern is easily extensible to include additional global providers (e.g., theming, state management, data fetching) as the application grows.
*   **Status Monitoring:** The Header component includes UI indicators for Auth and DB connection status. The DB status is checked by fetching an API endpoint (`/api/debug/supabase-test`, path confirmed, typo `/api/debug/supabase-direct` corrected in Header fetch). This endpoint (`supabase-test/route.ts`) performs a connection check and basic CRUD test using the `supabaseAdmin` client.
*   **Environment Management:** Environment variables are centralized and accessed via `src/lib/env.ts`. This module provides default fallbacks and includes a `validate()` method to check for critical missing variables at runtime (used in `layout.tsx`). Client-side variables are correctly prefixed with `NEXT_PUBLIC_`.
*   **API Authorization:** Server-side helper functions in `src/lib/auth-helpers.ts` enforce authorization in API routes using `getServerSession` and `supabaseAdmin` for permission checks.
*   **Supabase Admin Client:** Initialized in `lib/supabase-admin.ts` using the service role key, bypassing RLS for privileged server-side operations. Uses generated DB types.
*   **Service Layer:** Business logic and database interactions for specific entities are encapsulated in service files within `src/lib/` (e.g., `user-service.ts`, `world-service.ts`). These services typically use the `supabaseAdmin` client for direct data operations.
    *   `user-service.ts` handles user CRUD/stats, including NextAuth ID reconciliation.
    *   `world-service.ts` handles world CRUD. It also includes data transformation logic and a function (`generateWorld`) that delegates complex tasks (AI world generation) to an external `INTERNAL_API_URL`.
    *   `character-service.ts` handles character CRUD, supporting both 'player' and 'ai' character types and linking characters to users and worlds.
*   **Client Data Fetching Pattern:** Client components often use dedicated fetcher functions (e.g., `fetchWorlds` in `world-service.ts`) that call internal Next.js API routes, rather than interacting with Supabase directly. The API routes then utilize the server-side services.
    *   No direct client-side Supabase client is currently implemented; all client data access is routed through internal API endpoints for security and consistency.
*   **AI Integration (External API):** Complex AI tasks like world generation are handled by an external/internal service accessed via `INTERNAL_API_URL`, as seen in `world-service.ts`.
    *   The app makes a POST request to an endpoint (e.g., `INTERNAL_API_URL/create_world`) with user and world data.
    *   The external service performs the AI/complex logic and returns the generated world, which is then used by the app.
    *   This pattern decouples AI/business logic from the main app, allowing for independent scaling and updates.

## API Endpoints Overview

The API follows RESTful conventions and is organized by resource:

- **Users**
  - `GET /api/v1/users` — List all users (admin only)
  - `POST /api/v1/users` — Create a new user (admin only)
  - `GET /api/v1/users/[userId]` — Get user details
  - **Nested resources:**
    - `GET /api/v1/users/[userId]/characters` — List a user's characters
    - `GET /api/v1/users/[userId]/stats` — Get a user's stats
    - `GET /api/v1/users/[userId]/worlds` — List a user's worlds

- **Characters**
  - `GET /api/v1/characters` — List all characters
  - `POST /api/v1/characters` — Create a new character
  - `GET /api/v1/characters/[characterId]` — Get character details
  - `PUT /api/v1/characters/[characterId]` — Update a character
  - `DELETE /api/v1/characters/[characterId]` — Delete a character

- **Worlds**
  - `GET /api/v1/worlds` — List all worlds
  - `POST /api/v1/worlds` — Create a new world
  - `GET /api/v1/worlds/[worldId]` — Get world details
  - `PUT /api/v1/worlds/[worldId]` — Update a world
  - `DELETE /api/v1/worlds/[worldId]` — Delete a world
  - **Nested resources:**
    - `GET /api/v1/worlds/[worldId]/messages` — List messages for a world (with pagination and filtering)

- **Stats**
  - `GET /api/v1/stats` — List or aggregate stats

**Patterns and Notes:**
- All endpoints use RESTful resource-based routing.
- Nested resources are used for user-specific and world-specific data.
- Admin checks are enforced for sensitive operations (e.g., listing/creating users).
- Pagination and filtering are supported for large collections (e.g., messages).
- All endpoints are implemented as Next.js API routes, using async functions for HTTP verbs.
- Data access is performed via the service layer and supabaseAdmin client for consistency and security.
- Error handling and permission checks are consistently applied.

---

## Key Technical Decisions & Patterns

*   **Framework:** Next.js (App Router) provides routing, server-side rendering, API routes, and build optimization.
*   **Language:** TypeScript is used for static typing across the codebase (`tsconfig.json`, `.ts`/`.tsx` files).
*   **API Design:** RESTful API principles are followed for backend endpoints (`src/app/api/v1/`). Resource-based routing is evident (e.g., `/users`, `/characters`, `/worlds`).
*   **Data Management:**
    *   Supabase client library (`lib/supabase-admin.ts`, potentially a client-side instance elsewhere) interacts with the database.
    *   Service layer (`lib/*-service.ts`) likely encapsulates database interaction logic for different entities (users, characters, worlds).
    *   Database schema is defined in `supabase/schema.sql`.
*   **State Management:** React Context API is likely used via `src/components/Providers.tsx` for global state. Client-side hooks (`useSWR`, `React Query`, or custom hooks like `src/app/worlds/hooks/`) may be used for feature-specific state and data fetching.
*   **Styling:** Tailwind CSS is used as the base. A detailed theme (colors, fonts) is defined using CSS variables in `globals.css`. Custom utility classes (`.fantasy-button`, `.fantasy-card`, `.medieval-text`, `.message-*`, `.scroll-parchment`) are created using `@apply` in `globals.css` to build reusable themed components. Specific fonts (`Cinzel`, `MedievalSharp`, `Roboto`) are imported via Google Fonts. A dark theme with a background image is applied globally.
    *   Theming is managed via CSS variables for primary, secondary, accent, background, and text colors, as well as font families.
    *   Utility classes are defined for fantasy-themed buttons, cards, inputs, message bubbles, loading animations, and scroll-parchment backgrounds.
    *   Accessibility is addressed with focus styles, caret/selection management, and base typography.
    *   Animations (e.g., fade-slide-in, loading dots) are defined for enhanced UI feedback.
*   **Component Structure:** Reusable UI components are in `src/components/`. Feature-specific components are co-located with routes. Page-specific components may be defined within the page file. Themed styling relies heavily on custom classes from `globals.css`.
*   **Environment Variables:** Centralized management in `src/lib/env.ts`.

## Component Relationships (High-Level)

*   **Pages (`src/app/**/page.tsx`):** Entry points for user views. Can be server or client components.
*   **Layout (`src/app/layout.tsx`):** Defines the root HTML structure (server component), includes global `Header`, `Footer`, and wraps content with `Providers`. Validates environment variables.
*   **Header (`src/components/Header.tsx`):** Client component responsible for top navigation, displaying auth status/user info using `useSession`, handling profile dropdown/sign out, and showing DB status. Relies on `SessionProvider` from `Providers.tsx`.
*   **Providers (`src/components/Providers.tsx`):** Client component that wraps the application and provides the `SessionProvider` from `next-auth/react`.
*   **API Routes (`src/app/api/**/route.ts`):** Handle backend logic, interact with services and the database.
*   **Services (`src/lib/*-service.ts`):** Abstract database operations for specific data models.
*   **Supabase Client (`lib/supabase-*.ts`):** Provides the interface to the Supabase backend.
*   **UI Components (`src/components`, `src/app/**/components`):** Render the user interface elements.
*   **Hooks (`src/app/**/hooks`):** Encapsulate reusable logic, often related to data fetching or state management within specific features.

## Critical Implementation Paths

*   User Authentication Flow (Signup -> Login -> Session Management).
*   CRUD operations for Core Entities (Users, Characters, Worlds).
*   Interaction between Frontend Components, API Routes, and Services.
*   Supabase Database Interaction and Schema Management.
