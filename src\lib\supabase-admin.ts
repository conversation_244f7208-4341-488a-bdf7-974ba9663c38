import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from './database.types';
import { env } from './env';

// Log environment status at initialization time
// console.log('[SupabaseAdmin] Initializing with environment status:', {
//   urlSet: <PERSON><PERSON><PERSON>(env.SUPABASE_URL),
//   serviceKeySet: <PERSON><PERSON><PERSON>(env.SUPABASE_SERVICE_KEY),
//   urlLength: env.SUPABASE_URL.length,
//   serviceKeyLength: env.SUPABASE_SERVICE_KEY.length
// });

// // Add debugging to see if env variables are loaded
// console.log('Initializing supabaseAdmin with env values:');
// console.log('SUPABASE_URL present:', !!env.SUPABASE_URL);
// console.log('SUPABASE_SERVICE_KEY present:', !!env.SUPABASE_SERVICE_KEY);

// Check if the Supabase URL and service role key are set
if (!env.SUPABASE_URL || !env.SUPABASE_SERVICE_KEY) {
  console.error('[SupabaseAdmin] Missing Supabase URL or service role key. Check your NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
  console.error('[SupabaseAdmin] You can find this key in your Supabase project under Project Settings > API > service_role key');
}

// Create a Supabase client with the service role key that can bypass RLS
// Only create the client if both URL and service key are available
let supabaseAdmin: SupabaseClient<Database>;

if (env.SUPABASE_URL && env.SUPABASE_SERVICE_KEY) {
  try {
    supabaseAdmin = createClient<Database>(
      env.SUPABASE_URL,
      env.SUPABASE_SERVICE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    // console.log('[SupabaseAdmin] Client initialized successfully');
  } catch (error) {
    console.error('[SupabaseAdmin] Error initializing client:', error);
    throw new Error('Failed to initialize Supabase admin client');
  }
} else {
  // Create a dummy client that logs errors when used
  console.error('[SupabaseAdmin] Creating dummy client due to missing credentials');
  supabaseAdmin = new Proxy({} as any, {
    get: function(target, prop) {
      // Return a function that logs the error for any method called
      return function() {
        console.error(`[SupabaseAdmin] Client not initialized. Missing ${!env.SUPABASE_URL ? 'URL' : 'service role key'}.`);
        return Promise.resolve({
          data: null,
          error: new Error('Supabase admin client not properly initialized')
        });
      };
    }
  }) as SupabaseClient<Database>;
}

export { supabaseAdmin }; 