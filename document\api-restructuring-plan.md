# API Restructuring Plan for LLMDND Web Application

## Current Structure Issues
- Inconsistent nesting patterns between resources
- Unclear separation between collection and individual resource operations
- Some routes mix concerns and responsibilities
- Lack of versioning for future API changes

## Proposed New Structure

### API Root
- `/api/v1/` - Base path with versioning for all API endpoints

### Users API
- `/api/v1/users` (Collection)
  - `GET` - List all users (admin only)
  - `POST` - Create a new user

- `/api/v1/users/[userId]` (Individual Resource)
  - `GET` - Get user details
  - `PATCH` - Update user details
  - `DELETE` - Delete user

- `/api/v1/users/me` (Current User Resource)
  - `GET` - Get current user details
  - `PATCH` - Update current user

- `/api/v1/users/[userId]/stats` (User Stats)
  - `GET` - Get user stats

### Characters API
- `/api/v1/characters` (Collection)
  - `GET` - List all characters (admin only)
  - `POST` - Create a new character

- `/api/v1/characters/[characterId]` (Individual Resource)
  - `GET` - Get character details
  - `PATCH` - Update character details
  - `DELETE` - Delete character

- `/api/v1/users/[userId]/characters` (User's Characters)
  - `GET` - List all characters for a specific user
  - `POST` - Create a character for a specific user

### Worlds API
- `/api/v1/worlds` (Collection)
  - `GET` - List all worlds (admin only)
  - `POST` - Create a new world

- `/api/v1/worlds/[worldId]` (Individual Resource)
  - `GET` - Get world details
  - `PATCH` - Update world details
  - `DELETE` - Delete world

- `/api/v1/users/[userId]/worlds` (User's Worlds)
  - `GET` - List all worlds for a specific user
  - `POST` - Create a world for a specific user

### Authentication API
- `/api/v1/auth/login` - User login
- `/api/v1/auth/logout` - User logout
- `/api/v1/auth/register` - User registration
- `/api/v1/auth/refresh` - Refresh token

## Implementation Strategy
1. Create the new directory structure
2. Move and refactor existing endpoint handlers
3. Update frontend API client code to use new endpoints
4. Add redirect middleware from old endpoints to new endpoints for backwards compatibility
5. Gradually deprecate old endpoints after ensuring all clients are updated

## Directory Structure
```
src/
  app/
    api/
      v1/
        users/
          route.ts                    # Collection operations
          [userId]/
            route.ts                  # Individual resource operations
            stats/
              route.ts                # User stats operations
          me/
            route.ts                  # Current user operations
        characters/
          route.ts                    # Collection operations
          [characterId]/
            route.ts                  # Individual resource operations
        worlds/
          route.ts                    # Collection operations
          [worldId]/
            route.ts                  # Individual resource operations
        auth/
          login/
            route.ts
          logout/
            route.ts
          register/
            route.ts
          refresh/
            route.ts
      # Legacy endpoints (temporary redirects)
      users/
      characters/
      worlds/
      auth/
```

## Benefits of New Structure
1. **Consistent pattern** - All resources follow the same pattern
2. **Clear separation of concerns** - Each endpoint has a single responsibility
3. **Versioning** - Allows for future API changes without breaking clients
4. **Improved discoverability** - Easier for developers to navigate and understand
5. **RESTful** - Follows REST principles more closely
6. **Hierarchical** - Resources that belong to users are properly nested 