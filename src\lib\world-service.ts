import { supabaseAdmin } from './supabase-admin';
import { env } from './env';

/**
 * Makes a call to the internal API for world generation
 * Uses env.INTERNAL_API_URL environment variable for the API endpoint
 */
export async function generateWorld(
  userId: string,
  worldData: {
    name: string;
    theme?: string;
    worldCustomization?: string;
    dmType?: 'normal' | 'hardcore' | 'funny' | 'easygoing';
    playerCharacterCustomization?: string;
    aiCharacterCount: number;
    characterCustomizations?: string[];
  }
) {
  try {
    console.log("Generating world via internal API:", worldData);

    // Prepare the payload for the API call
    const payload = {
      userId,
      ...worldData
    };

    // Get API URL from env utility
    const apiUrl = env.INTERNAL_API_URL + "create_world";
    // console.log("payload", payload)
    // Make the API call to the internal API endpoint
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error from internal API:", errorData);
      throw new Error(errorData.message || "Failed to generate world");
    }

    // Get the generated world data from the response
    const generatedWorldData = await response.json();

    // Return the data directly from the internal API which already created the world in the database
    return generatedWorldData;
  } catch (error) {
    console.error('Error in generateWorld:', error);
    return null;
  }
}

/**
 * Gets all worlds for a specific user
 */
export async function getWorldsByUserId(userId: string) {
  try {
    console.log("getWorldsByUserId called with userId:", userId);

    // Use admin client to bypass RLS for consistency
    const { data, error } = await supabaseAdmin
      .from('worlds')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting worlds by user ID:', error);
      return [];
    }

    // console.log("Raw world data from database:", data);

    // Transform the data to match the expected format in the frontend
    const transformedWorlds = (data || []).map(world => {
      // Check if settings is a valid object
      const settings = typeof world.settings === 'object' ? world.settings : {};

      return {
        id: world.id,
        name: world.name,
        description: world.description,
        theme: settings?.theme || '',
        dmType: settings?.dmType || 'normal',
        createdAt: world.created_at,
        // Determine status based on settings - if generationComplete exists, it's completed
        status: settings?.generationComplete ? 'completed' : 'generating'
      };
    });

    // console.log("Transformed worlds:", transformedWorlds);
    return transformedWorlds;
  } catch (error) {
    console.error('Error in getWorldsByUserId:', error);
    return [];
  }
}

/**
 * Gets a world by its ID
 */
export async function getWorldById(id: string) {
  try {
    // Use admin client to bypass RLS for consistency
    const { data, error } = await supabaseAdmin
      .from('worlds')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error getting world by ID:', error);
      return null;
    }

    return data ? {
      id: data.id,
      name: data.name,
      description: data.description,
      theme: data.settings?.theme || '',
      dmType: data.settings?.dmType || 'normal',
      createdAt: data.created_at,
      status: data.settings?.generationComplete ? 'completed' : 'generating',
      settings: data.settings
    } : null;
  } catch (error) {
    console.error('Error in getWorldById:', error);
    return null;
  }
}

/**
 * Updates a world
 */
export async function updateWorld(
  id: string,
  updates: {
    name?: string;
    description?: string;
    settings?: any;
  }
) {
  try {
    // Use the admin client to bypass RLS for server-side operations
    const { data, error } = await supabaseAdmin
      .from('worlds')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error('Error updating world:', error);
      return null;
    }

    return data?.[0] || null;
  } catch (error) {
    console.error('Error in updateWorld:', error);
    return null;
  }
}

/**
 * Deletes a world
 */
export async function deleteWorld(id: string) {
  try {
    // Use admin client to bypass RLS for consistency
    const { error } = await supabaseAdmin
      .from('worlds')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting world:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteWorld:', error);
    return false;
  }
}

/**
 * Fetches worlds from the API (client-side)
 * This function should be used in client components instead of direct supabase calls
 */
export async function fetchWorlds(userId: string) {
  console.log("fetchWorlds called with userId:", userId);

  try {
    const response = await fetch(`/api/worlds?userId=${encodeURIComponent(userId)}`);

    console.log("API response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error fetching worlds from API:", errorData);
      throw new Error(errorData.error || 'Failed to fetch worlds');
    }

    const worlds = await response.json();
    // console.log("Worlds received from API:", worlds);

    return worlds;
  } catch (error) {
    console.error("Error in fetchWorlds:", error);
    return [];
  }
}