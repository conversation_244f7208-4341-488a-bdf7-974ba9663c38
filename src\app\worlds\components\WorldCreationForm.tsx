import React from 'react'
import { WorldCreationForm as WorldCreationFormType } from '@/types/world.types'
import { motion, AnimatePresence } from 'framer-motion'

interface WorldCreationFormProps {
  formState: WorldCreationFormType
  isLoading: boolean
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
  onCharacterCustomizationChange: (index: number, value: string) => void
  onSubmit: (e: React.FormEvent) => void
}

export function WorldCreationForm({
  formState,
  isLoading,
  onInputChange,
  onCharacterCustomizationChange,
  onSubmit,
}: WorldCreationFormProps) {
  return (
    <div className="fantasy-card">
      <div className="fantasy-card-header">
        <h2 className="text-xl font-bold">Create a New World</h2>
      </div>

      <div className="p-6">
        <form onSubmit={onSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-1 cursor-default">
              World Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formState.name}
              onChange={onInputChange}
              className="fantasy-input w-full"
              placeholder="Eldoria, Mystara, etc."
            />
          </div>

          <div>
            <label htmlFor="theme" className="block text-sm font-medium mb-1 cursor-default">
              Theme
            </label>
            <select
              id="theme"
              name="theme"
              value={formState.theme}
              onChange={onInputChange}
              className="fantasy-input w-full"
            >
              <option value="">Select a Theme</option>
              <option value="high-fantasy">High Fantasy</option>
              <option value="dark-fantasy">Dark Fantasy</option>
              <option value="steampunk">Steampunk</option>
              <option value="horror">Horror</option>
              <option value="post-apocalyptic">Post-Apocalyptic</option>
            </select>
          </div>

          <div>
            <label htmlFor="dmType" className="block text-sm font-medium mb-1 cursor-default">
              DM Type
            </label>
            <select
              id="dmType"
              name="dmType"
              value={formState.dmType}
              onChange={onInputChange}
              className="fantasy-input w-full"
            >
              <option value="normal">Normal</option>
              <option value="hardcore">Hardcore</option>
              <option value="funny">Funny</option>
              <option value="easygoing">Easy Going</option>
            </select>
          </div>

          <div>
            <label htmlFor="worldCustomization" className="block text-sm font-medium mb-1 cursor-default">
              World Customization
            </label>
            <textarea
              id="worldCustomization"
              name="worldCustomization"
              value={formState.worldCustomization}
              onChange={onInputChange}
              rows={3}
              className="fantasy-input w-full"
              placeholder="Add custom elements to your world..."
            />
          </div>

          <div>
            <label htmlFor="playerCharacterCustomization" className="block text-sm font-medium mb-1 cursor-default">
              Player Character Customization
            </label>
            <textarea
              id="playerCharacterCustomization"
              name="playerCharacterCustomization"
              value={formState.playerCharacterCustomization}
              onChange={onInputChange}
              rows={3}
              className="fantasy-input w-full"
              placeholder="Describe your own character..."
            />
          </div>

          <div>
            <label htmlFor="aiCharacterCount" className="block text-sm font-medium mb-1 cursor-default">
              AI Character Count (0-3)
            </label>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() =>
                  onInputChange({
                    target: {
                      name: 'aiCharacterCount',
                      value: String(formState.aiCharacterCount - 1),
                    },
                  } as any)
                }
                className="px-2 py-1 rounded bg-background-darker border border-gray-700 hover:bg-gray-700 transition"
              >
                ◀
              </button>
              <input
                type="number"
                id="aiCharacterCount"
                name="aiCharacterCount"
                min="0"
                max="3"
                value={formState.aiCharacterCount}
                onChange={onInputChange}
                className="fantasy-input w-16 border-2 border-primary rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200 no-spinner"
              />
              <button
                type="button"
                onClick={() =>
                  formState.aiCharacterCount < 3 &&
                  onInputChange({
                    target: {
                      name: 'aiCharacterCount',
                      value: String(formState.aiCharacterCount + 1),
                    },
                  } as any)
                }
                className="px-2 py-1 rounded bg-background-darker border border-gray-700 hover:bg-gray-700 transition"
              >
                ▶
              </button>
            </div>
            <p className="text-xs text-text-secondary mt-1">Choose how many AI companions to generate (max 3).</p>
          </div>

          {/* AnimatePresence for Character Customizations section */}
          <AnimatePresence initial={false}>
            {formState.aiCharacterCount > 0 && (
              <motion.div
                key="character-customizations-section"
                initial={{ opacity: 0, y: 16 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 16 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="overflow-hidden transition-all duration-300 ease-out transform mt-4"
              >
                <div className="space-y-3">
                  <label className="block text-sm font-medium mb-1 cursor-default">
                    Character Customizations
                  </label>

                  <AnimatePresence initial={false}>
                    {[...Array(formState.aiCharacterCount)].map((_, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 16 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 16 }}
                        transition={{ duration: 0.25, ease: 'easeOut' }}
                        className="border border-gray-800 rounded-lg p-3 bg-background-darker"
                      >
                        <label className="block text-sm font-medium mb-1 cursor-default">
                          Character {index + 1}
                        </label>
                        <textarea
                          id={`characterCustomizations-${index}`}
                          value={formState.characterCustomizations[index]}
                          onChange={(e) => onCharacterCustomizationChange(index, e.target.value)}
                          rows={2}
                          className="fantasy-input w-full"
                          placeholder={`Describe character ${index + 1}...`}
                        />
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <button
            type="submit"
            disabled={isLoading}
            className="fantasy-button w-full"
          >
            {isLoading ? (
              <div className="loading-dots">
                <div></div>
                <div></div>
                <div></div>
              </div>
            ) : (
              'Generate World'
            )}
          </button>
        </form>
      </div>
    </div>
  )
}
