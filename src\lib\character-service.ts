import { supabaseAdmin } from './supabase-admin';

/**
 * Creates a new character for a user
 */
export async function create<PERSON><PERSON>cter(
  userId: string,
  name: string,
  race: string,
  characterClass: string,
  level: number = 1,
  worldId: string | null = null,
  attributes: any = {},
  type: 'player' | 'ai' = 'player'
) {
  try {
    const { data, error } = await supabaseAdmin
      .from('characters')
      .insert({
        name,
        race,
        class: characterClass,
        level,
        type,
        user_id: userId,
        world_id: worldId,
        attributes,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select();

    if (error) {
      console.error('Error creating character:', error);
      return null;
    }

    return data?.[0] || null;
  } catch (error) {
    console.error('Error in createCharacter:', error);
    return null;
  }
}

/**
 * Gets all characters for a specific user
 */
export async function getCharactersByUserId(userId: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('characters')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting characters by user ID:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getCharactersByUserId:', error);
    return [];
  }
}

/**
 * Gets all characters for a specific world
 */
export async function getCharactersByWorldId(worldId: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('characters')
      .select('*')
      .eq('world_id', worldId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting characters by world ID:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getCharactersByWorldId:', error);
    return [];
  }
}

/**
 * Gets a character by its ID
 */
export async function getCharacterById(id: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('characters')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error getting character by ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getCharacterById:', error);
    return null;
  }
}

/**
 * Updates a character
 */
export async function updateCharacter(
  id: string,
  updates: {
    name?: string;
    race?: string;
    class?: string;
    level?: number;
    type?: 'player' | 'ai';
    world_id?: string | null;
    attributes?: any;
  }
) {
  try {
    const { data, error } = await supabaseAdmin
      .from('characters')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select();

    if (error) {
      console.error('Error updating character:', error);
      return null;
    }

    return data?.[0] || null;
  } catch (error) {
    console.error('Error in updateCharacter:', error);
    return null;
  }
}

/**
 * Deletes a character
 */
export async function deleteCharacter(id: string) {
  try {
    const { error } = await supabaseAdmin
      .from('characters')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting character:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteCharacter:', error);
    return false;
  }
} 