# SoloQuest API v1

This directory contains the v1 version of the SoloQuest application API. The API follows REST principles and is organized hierarchically by resource type.

## API Architecture

The API is structured following these principles:

1. **Versioned APIs** - All endpoints are under `/api/v1` to support future API evolution
2. **Resource-Based Design** - Endpoints represent resources (users, characters, worlds)
3. **Hierarchical Structure** - Resources that belong to other resources are nested appropriately
4. **Uniform Interface** - Consistent HTTP methods across resources:
   - `GET` - Retrieve resources
   - `POST` - Create resources
   - `PATCH` - Update resources partially
   - `DELETE` - Remove resources
5. **Clear Separation of Concerns** - Each endpoint has focused responsibility
6. **Authentication Required** - Most endpoints require authentication

## Endpoint Overview

### Users

- `GET /api/v1/users` - List all users (admin)
- `GET /api/v1/users/[userId]` - Get user details
- `PATCH /api/v1/users/[userId]` - Update user details
- `DELETE /api/v1/users/[userId]` - Delete user
- `GET /api/v1/users/me` - Get current user details
- `PATCH /api/v1/users/me` - Update current user

### Characters

- `GET /api/v1/characters` - List all characters (admin)
<!-- - `POST /api/v1/characters` - Create a new character -->
- `GET /api/v1/characters/[characterId]` - Get character details
- `PATCH /api/v1/characters/[characterId]` - Update character details
- `DELETE /api/v1/characters/[characterId]` - Delete character
- `GET /api/v1/users/[userId]/characters` - List user's characters
<!-- - `POST /api/v1/users/[userId]/characters` - Create character for user -->

### Worlds

- `GET /api/v1/worlds` - List all worlds (admin)
<!-- - `POST /api/v1/worlds` - Create a new world -->
- `GET /api/v1/worlds/[worldId]` - Get world details
- `PATCH /api/v1/worlds/[worldId]` - Update world details
- `GET /api/v1/worlds/[worldId]/messages` - Get world messages
- `DELETE /api/v1/worlds/[worldId]` - Delete world
- `GET /api/v1/users/[userId]/worlds` - List user's worlds
- `POST /api/v1/users/[userId]/worlds` - Create world for user (calls internal world generation API)
<!-- 
### Authentication

- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh token -->

## Internal APIs

The application uses internal services for several key operations:

1. **World Generation API** - When a world is created, the public API routes call an internal service (`generateWorld`) that performs the actual world creation logic. This separation allows for:
   - Better encapsulation of business logic
   - Easier testing and mocking
   - Future extensibility for different generation approaches
   
   Currently, this is implemented as a mock service, but it will eventually connect to more sophisticated generation systems.

## Migration Process

This API structure is a restructuring of the original API. Migration follows these steps:

1. Implement new API endpoints alongside existing ones
2. Create compatibility redirects from old to new endpoints
3. Update frontend code to use new endpoints
4. Gradually phase out old endpoints

## Error Handling

All endpoints follow a consistent error response format:

```json
{
  "error": "Error message describing what went wrong"
}
```

HTTP status codes are used appropriately:
- 200 - Success
- 201 - Resource created
- 400 - Bad request (client error)
- 401 - Unauthorized (not authenticated)
- 403 - Forbidden (authenticated but not authorized)
- 404 - Resource not found
- 409 - Conflict (e.g., resource already exists)
- 500 - Server error

## Authentication

Authentication is handled using NextAuth.js. Most endpoints require a valid session, which is checked using `getServerSession(authOptions)`.

## Further Development

Future API improvements may include:
- Pagination for collection endpoints 
- Advanced filtering and sorting options
- Rate limiting
- API documentation using OpenAPI/Swagger
- WebSocket APIs for real-time features 