import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireWorldOwnerOrAdmin } from '@/lib/auth-helpers';
import { env } from '@/lib/env';
import type { Message } from '@/types/schema.types';



type MessageRequest = {
  content: string;
  world_id: string;
  character_id?: string;
  metadata?: {
    rolls?: {
      type: string;
      value: number;
      modifier?: number;
    }[];
    actions?: string[];
  };
};

/**
 * GET /api/v1/worlds/[worldId]/messages
 * Get world messages
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { worldId } = await params;
    await requireWorldOwnerOrAdmin(session, worldId);

    // Query URL parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const characterId = searchParams.get('character_id');

    // Build query for this world
    let query = supabaseAdmin
      .from('messages')
      .select('*', { count: 'exact' })
      .eq('world_id', worldId)
      .order('created_at', { ascending: false });

    // Add filter by character if provided
    if (characterId) {
      query = query.eq('character_id', characterId);
    }

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    const { data: messages, error: messagesError, count } = await query;

    if (messagesError) {
      return NextResponse.json({ error: messagesError.message }, { status: 500 });
    }

    return NextResponse.json({
      messages: messages as Message[],
      count,
      pagination: {
        limit,
        offset,
        total: count
      }
    });
  } catch (error) {
    console.error('Error fetching world messages:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/worlds/[worldId]/message
 * Send a message in a campaign
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    // Authentication & Authorization
    const session = await requireUserSession(request);
    const { worldId } = await params;
    await requireWorldOwnerOrAdmin(session, worldId);

    // Validate request body
    const messageData: MessageRequest = await request.json();

    if (!messageData.content) {
      return NextResponse.json(
        { error: 'Missing required field: content' },
        { status: 400 }
      );
    }

    // Forward the request to internal api
    try {
      console.log("Generating missage via internal API:", messageData);

      // Prepare the payload for the API call
      const payload = {
        worldId,
        ...messageData
      };

      // Get API URL from env utility
      const apiUrl = env.INTERNAL_API_URL + "chat";

      // Make the API call to the internal API endpoint
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error from internal API:", errorData);
        throw new Error(errorData.message || "Failed to generate message");
      }

      // Get the message request status from the response
      const messageSuccess = await response.json();

      // Return the data with 201 status code
      return NextResponse.json(messageSuccess, { status: 201 });
    } catch (error) {
      console.error('Error in generateMessage:', error);
      return NextResponse.json(
        { error: 'Failed to create message' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in POST /messages:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
