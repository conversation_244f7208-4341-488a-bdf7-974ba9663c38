export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string; // UUID
          email: string;
          name: string | null;
          avatar_url: string | null;
          is_admin: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string; // UUID
          email: string;
          name?: string | null;
          avatar_url?: string | null;
          is_admin?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string | null;
          avatar_url?: string | null;
          is_admin?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      worlds: {
        Row: {
          id: string; // UUID
          name: string;
          description: string | null;
          dm_prompt: string | null;
          memories: string;
          settings: any;
          user_id: string; // UUID
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string; // UUID generates by default
          name: string;
          description?: string | null;
          dm_prompt?: string | null;
          memories?: string;
          settings?: any;
          user_id: string; // UUID
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          dm_prompt?: string | null;
          memories?: string;
          settings?: any;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      characters: {
        Row: {
          id: string; // UUID
          type: 'player' | 'ai';
          name: string;
          gender: string;
          race: string;
          class: string;
          background: string;
          alignment: string;
          attributes: any;
          inventory: string;
          description: string;
          memories: string;
          user_id: string; // UUID
          world_id: string | null; // UUID
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string; // UUID generates by default
          type?: 'player' | 'ai';
          name: string;
          gender: string;
          race: string;
          class: string;
          background: string;
          alignment: string;
          attributes?: any;
          inventory?: string;
          description: string;
          memories?: string;
          user_id: string; // UUID
          world_id?: string | null; // UUID
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          type?: 'player' | 'ai';
          name?: string;
          gender?: string;
          race?: string;
          class?: string;
          background?: string;
          alignment?: string;
          attributes?: any;
          inventory?: string;
          description?: string;
          memories?: string;
          user_id?: string;
          world_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      campaigns: {
        Row: {
          id: string; // UUID
          description: string | null;
          world_id: string; // UUID
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string; // UUID generates by default
          description?: string | null;
          world_id: string; // UUID
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          description?: string | null;
          world_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      messages: {
        Row: {
          id: number;
          content: string;
          sender: string;
          world_id: string | null; // UUID
          user_id: string | null; // UUID
          character_id: string | null; // UUID
          created_at: string;
        };
        Insert: {
          id?: number;
          content: string;
          sender: string;
          world_id?: string | null; // UUID
          user_id?: string | null; // UUID
          character_id?: string | null; // UUID
          created_at?: string;
        };
        Update: {
          id?: number;
          content?: string;
          sender?: string;
          world_id?: string | null;
          user_id?: string | null;
          character_id?: string | null;
          created_at?: string;
        };
      };
      n8n_chat: {
        Row: {
          id: number;
          session_id: string; // UUID
          message: any;
        };
        Insert: {
          id?: number;
          session_id: string; // UUID
          message: any;
        };
        Update: {
          id?: number;
          session_id?: string;
          message?: any;
        };
      };
    };
  };
}; 