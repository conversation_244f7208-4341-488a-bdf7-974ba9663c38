'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useSupabaseClient } from '@/lib/supabase-client'

type Message = {
  id: number
  content: string
  sender: string
  world_id?: string
  user_id?: string
  character_id?: string
  created_at: string
}

export default function TestRlsPage() {
  const { data: session, status } = useSession()
  const supabase = useSupabaseClient()
  
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // For RLS security test
  const [securityTestResult, setSecurityTestResult] = useState<{
    attempted: boolean;
    success: boolean;
    message: string;
    data: any;
  } | null>(null)
  
  // Fetch messages when authenticated
  useEffect(() => {
    async function fetchMessages() {
      if (status !== 'authenticated') return
      
      try {
        setLoading(true)
        setError(null)
        
        // Fetch messages using the authenticated client
        const { data, error } = await supabase
          .from('messages')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(20)
        
        if (error) {
          console.error('Error fetching messages:', error)
          setError(`Failed to fetch messages: ${error.message}`)
          return
        }
        
        setMessages(data || [])
      } catch (err: any) {
        console.error('Exception fetching messages:', err)
        setError(`Error: ${err.message}`)
      } finally {
        setLoading(false)
      }
    }
    
    fetchMessages()
  }, [supabase, status])
  
  // Create a new message
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session?.user?.id) {
      setError('You must be signed in to create messages')
      return
    }
    
    if (!newMessage.trim()) {
      setError('Message cannot be empty')
      return
    }
    
    try {
      setLoading(true)
      setError(null)
      setSuccess(null)
      
      // Insert the new message using the authenticated client
      const { data, error } = await supabase
        .from('messages')
        .insert({
          content: newMessage,
          sender: session.user.name || 'Anonymous',
          user_id: session.user.id
        })
        .select()
      
      if (error) {
        console.error('Error creating message:', error)
        setError(`Failed to create message: ${error.message}`)
        return
      }
      
      // Add the new message to the list
      if (data && data.length > 0) {
        setMessages(prev => [data[0], ...prev])
        setNewMessage('')
        setSuccess('Message created successfully')
      }
    } catch (err: any) {
      console.error('Exception creating message:', err)
      setError(`Error: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }
  
  // Test RLS security by attempting to access all messages directly
  const testRlsSecurity = async () => {
    if (!session?.user?.id) {
      setError('You must be signed in to run this test')
      return
    }
    
    try {
      setSecurityTestResult({
        attempted: true,
        success: false,
        message: 'Running security test...',
        data: null
      })
      
      // Attempt to bypass RLS by directly querying without filters
      // This should fail if RLS is working correctly
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .neq('user_id', session.user.id) // Try to get messages NOT belonging to current user
        .limit(5)
      
      if (error) {
        // An error here is actually good - it means RLS prevented the query
        setSecurityTestResult({
          attempted: true,
          success: true, // Test successful because it failed to retrieve data
          message: `RLS is working correctly! Access denied with error: ${error.message}`,
          data: error
        })
      } else {
        // If we got data that doesn't belong to the user, RLS is not working
        const unauthorizedMessages = data?.filter(m => m.user_id !== session.user.id) || []
        
        if (unauthorizedMessages.length > 0) {
          setSecurityTestResult({
            attempted: true,
            success: false, // Test failed because it retrieved unauthorized data
            message: `SECURITY ISSUE: Retrieved ${unauthorizedMessages.length} messages that don't belong to you!`,
            data: unauthorizedMessages
          })
        } else {
          setSecurityTestResult({
            attempted: true,
            success: true,
            message: 'RLS is working correctly! No unauthorized messages were retrieved.',
            data: null
          })
        }
      }
    } catch (err: any) {
      console.error('Exception during security test:', err)
      setSecurityTestResult({
        attempted: true,
        success: false,
        message: `Error during security test: ${err.message}`,
        data: err
      })
    }
  }
  
  if (status === 'loading') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">RLS Test Page</h1>
        <div className="flex items-center justify-center h-40">
          <p className="text-gray-500">Loading session...</p>
        </div>
      </div>
    )
  }
  
  if (status === 'unauthenticated') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">RLS Test Page</h1>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <p className="text-yellow-700">You must be signed in to view this page.</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="max-w-4xl mx-auto p-6 bg-purple-900 text-white">
      <h1 className="text-2xl font-bold mb-6 text-white">RLS Test Page</h1>
      
      <div className="mb-8 bg-purple-800 p-4 rounded-md border border-purple-600">
        <h2 className="text-lg font-semibold mb-2 text-white">User Information</h2>
        <p>ID: <span className="font-mono text-purple-200">{session?.user?.id}</span></p>
        <p>Email: {session?.user?.email}</p>
        <p>JWT Available: {session?.supabaseAccessToken ? 'Yes' : 'No'}</p>
      </div>
      
      {/* RLS Security Test */}
      <div className="mb-8 bg-purple-800 p-4 rounded-md border border-purple-600">
        <h2 className="text-lg font-semibold mb-4 text-white">RLS Security Test</h2>
        <p className="mb-4 text-purple-200">
          This test attempts to access messages that don't belong to you. 
          If RLS is working correctly, this should fail.
        </p>
        
        <button
          onClick={testRlsSecurity}
          className="px-4 py-2 bg-orange-600 text-white font-medium rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Run Security Test
        </button>
        
        {securityTestResult?.attempted && (
          <div className={`mt-4 p-4 rounded-md ${
            securityTestResult.success 
              ? 'bg-green-800 border border-green-600' 
              : 'bg-red-800 border border-red-600'
          }`}>
            <h3 className={`font-semibold ${
              securityTestResult.success ? 'text-green-200' : 'text-red-200'
            }`}>
              {securityTestResult.success ? '✅ Test Passed' : '❌ Test Failed'}
            </h3>
            <p className="mt-1 text-white">{securityTestResult.message}</p>
            
            {securityTestResult.data && !securityTestResult.success && (
              <div className="mt-2">
                <p className="font-semibold text-red-200">Unauthorized data retrieved:</p>
                <pre className="mt-1 bg-red-900 p-2 rounded text-xs overflow-auto max-h-40 text-white">
                  {JSON.stringify(securityTestResult.data, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Message creation form */}
      <div className="mb-8 bg-purple-800 p-4 rounded-md border border-purple-600">
        <h2 className="text-lg font-semibold mb-4 text-white">Create New Message</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-purple-200 mb-1">
              Message Content
            </label>
            <textarea
              id="message"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="w-full px-3 py-2 border border-purple-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-purple-900 text-white"
              rows={3}
              placeholder="Enter a message..."
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Message'}
          </button>
        </form>
        
        {/* Status messages */}
        {error && (
          <div className="mt-4 bg-red-800 border border-red-600 text-red-200 p-3 rounded">
            {error}
          </div>
        )}
        {success && (
          <div className="mt-4 bg-green-800 border border-green-600 text-green-200 p-3 rounded">
            {success}
          </div>
        )}
      </div>
      
      {/* Messages display */}
      <div className="bg-purple-800 p-4 rounded-md border border-purple-600">
        <h2 className="text-lg font-semibold mb-4 text-white">Your Messages</h2>
        
        {loading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-40">
            <p className="text-purple-200">Loading messages...</p>
          </div>
        ) : messages.length > 0 ? (
          <ul className="space-y-4">
            {messages.map((message) => (
              <li 
                key={message.id} 
                className="border border-purple-600 rounded-md p-4 bg-purple-700 shadow-sm"
              >
                <div className="flex justify-between mb-2">
                  <span className="font-semibold text-white">{message.sender}</span>
                  <span className="text-sm text-purple-300">
                    {new Date(message.created_at).toLocaleString()}
                  </span>
                </div>
                <p className="text-white">{message.content}</p>
                <div className="mt-2 text-xs text-purple-300">
                  <p>ID: {message.id}</p>
                  <p>User ID: {message.user_id}</p>
                  {message.world_id && <p>World ID: {message.world_id}</p>}
                  {message.character_id && <p>Character ID: {message.character_id}</p>}
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="bg-purple-700 border border-purple-600 rounded-md p-8 text-center">
            <p className="text-purple-200">No messages found. Create your first message above!</p>
          </div>
        )}
      </div>
    </div>
  )
} 