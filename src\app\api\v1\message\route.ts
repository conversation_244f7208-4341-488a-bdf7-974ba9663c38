import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireWorldOwnerOrAdmin } from '@/lib/auth-helpers';
import type { Message } from '@/types/schema.types';

type MessageRequest = {
  content: string;
  world_id: string;
  character_id?: string;
  metadata?: {
    rolls?: {
      type: string;
      value: number;
      modifier?: number;
    }[];
    actions?: string[];
  };
};

/**
 * POST /api/v1/message
 * Send a message in a campaign
 */
export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate user
    const session = await requireUserSession(request);
    
    // 2. Parse request body
    const messageData: MessageRequest = await request.json();
    
    // 3. Validate required fields
    if (!messageData.content || !messageData.world_id) {
      return NextResponse.json(
        { error: 'Missing required fields: content and world_id are required' },
        { status: 400 }
      );
    }
    
    // 4. Authorize world access
    await requireWorldOwnerOrAdmin(session, messageData.world_id);
    
    // 5. Save player message
    const { data: message, error: saveError } = await supabaseAdmin
      .from('messages')
      .insert({
        content: messageData.content,
        world_id: messageData.world_id,
        character_id: messageData.character_id,
        user_id: session.user.id,
        sender: 'player',
        metadata: messageData.metadata || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
      
    if (saveError) {
      console.error('Error saving message:', saveError);
      return NextResponse.json({ error: saveError.message }, { status: 500 });
    }
    
    // 6. Return success with message ID
    const m = message as Message;
    return NextResponse.json({
      success: true,
      message_id: m.id,
      timestamp: m.created_at
    }, { status: 201 });
    
  } catch (error: any) {
    console.error('Error in POST /api/v1/message:', error);
    // Handle specific auth errors
    if (error.message?.includes('Unauthorized') || error.status === 401) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (error.message?.includes('Forbidden') || error.status === 403) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 