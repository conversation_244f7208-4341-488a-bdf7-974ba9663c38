/**
 * Environment variable utility to handle loading environment variables in Next.js
 * This helps with potential issues related to server/client environment loading
 */

// Load environment variables with fallbacks
export const env = {
  // Supabase
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  SUPABASE_JWT_SECRET: process.env.SUPABASE_JWT_SECRET || '',
  
  // NextAuth
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || '',
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || '',
  
  // Google OAuth
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
  
  // Internal APIs
  INTERNAL_API_URL: process.env.INTERNAL_API_URL || '',
  
  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Helper methods
  isProduction: () => process.env.NODE_ENV === 'production',
  isDevelopment: () => process.env.NODE_ENV === 'development',
  
  // Validation method to check if critical variables are set
  validate: () => {
    const missing = [];
    
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) missing.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) missing.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) missing.push('SUPABASE_SERVICE_ROLE_KEY');
    if (!process.env.SUPABASE_JWT_SECRET) missing.push('SUPABASE_JWT_SECRET');
    if (!process.env.NEXTAUTH_URL) missing.push('NEXTAUTH_URL');
    if (!process.env.NEXTAUTH_SECRET) missing.push('NEXTAUTH_SECRET');
    if (!process.env.INTERNAL_API_URL) missing.push('INTERNAL_API_URL');
    
    return {
      isValid: missing.length === 0,
      missing
    };
  }
}; 