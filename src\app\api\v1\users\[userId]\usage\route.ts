import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import { getUserUsageStats, getUserWorldCount, getUserCharacterCount } from '@/lib/usage-tracking.service';

/**
 * GET /api/v1/users/[userId]/usage
 * Get user's usage statistics and limits
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Get basic usage stats (only messages are limited in new structure)
    const usageStats = await getUserUsageStats(targetUserId);

    // Get world and character counts for display (no limits enforced)
    const [worldCount, characterCount] = await Promise.all([
      getUserWorldCount(targetUserId),
      getUserCharacterCount(targetUserId)
    ]);

    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: usageStats.subscription_tier,
      usage: {
        messages_monthly: usageStats.messages_monthly,
        worlds_generation_weekly: usageStats.worlds_generation_weekly,
        worlds_total: {
          allowed: true,
          current: worldCount,
          limit: -1, // Unlimited
          remaining: -1,
          message: undefined
        },
        characters_total: {
          allowed: true,
          current: characterCount,
          limit: -1, // Unlimited
          remaining: -1,
          message: undefined
        }
      }
    });
  } catch (error) {
    console.error('Error getting user usage:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
