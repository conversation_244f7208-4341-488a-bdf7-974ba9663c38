'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useUserWorlds } from './hooks/useUserWorlds'
import { useWorldForm } from './hooks/useWorldForm'
import { WorldCreationForm } from './components/WorldCreationForm'
import { WorldsList } from './components/WorldsList'

export default function WorldsPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { worlds, isLoading: isWorldsLoading, mutate } = useUserWorlds()
  const {
    formState,
    handleInputChange,
    handleCharacterCustomizationChange,
    resetForm,
  } = useWorldForm()

  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/signup')
    }
  }, [status, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!session?.user?.id) return

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/v1/users/me/worlds`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formState,
          userId: session.user.id,
          characterCustomizations: formState.characterCustomizations.slice(0, formState.aiCharacterCount),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create world')
      }

      resetForm()
      await mutate()
    } catch (error) {
      console.error('Error creating world:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    router.push('/signup')
    return null
  }

  return (
    <div className="max-w-6xl mx-auto">
      <h1 className="text-3xl md:text-4xl font-bold mb-8 text-center">
        Forge Your <span className="text-primary">Realm</span>
      </h1>

      <div className="grid md:grid-cols-2 gap-8">
        <WorldCreationForm
          formState={formState}
          isLoading={isSubmitting}
          onInputChange={handleInputChange}
          onCharacterCustomizationChange={handleCharacterCustomizationChange}
          onSubmit={handleSubmit}
        />

        <div>
          <div className="fantasy-card">
            <div className="fantasy-card-header">
              <h2 className="text-xl font-bold">Your Worlds</h2>
            </div>
            <div className="p-4">
              <WorldsList worlds={worlds} isLoading={isWorldsLoading} />
            </div>
          </div>

          <div className="mt-6 p-4 scroll-parchment">
            <h3 className="text-lg font-bold mb-2 text-secondary">Worldbuilding Tips</h3>
            <ul className="list-disc list-inside text-sm space-y-2 text-text-secondary">
              <li>Leave fields blank—our AI fills the details!</li>
              <li>Only AI Character Count is required (0-3 companions)</li>
              <li>Add World Customization for custom themes or elements</li>
              <li>Describe characters briefly (&quot;wise wizard&quot;) or in detail</li>
              <li>World generation takes approximately 1 minute</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
