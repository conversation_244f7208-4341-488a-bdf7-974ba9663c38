import { createClient } from '@supabase/supabase-js';
import { useSession, signIn } from 'next-auth/react';
import { useEffect, useState, useRef, useCallback } from 'react';
import type { Database } from './database.types';

// Create a Supabase client without authentication initially
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Global client instances cache
let anonClientInstance: ReturnType<typeof createClient<Database>> | null = null;
const authClientInstances = new Map<string, ReturnType<typeof createClient<Database>>>();

/**
 * Creates a Supabase client with the user's session token using a singleton pattern
 */
export const createSupabaseClient = (supabaseAccessToken?: string) => {
  // If no token is provided, return the anonymous client instance
  if (!supabaseAccessToken) {
    if (!anonClientInstance) {
      anonClientInstance = createClient<Database>(supabaseUrl, supabaseAnonKey);
    }
    return anonClientInstance;
  }

  // Check if we already have a client for this token
  if (!authClientInstances.has(supabaseAccessToken)) {
    // Create options with JWT token for both global and realtime
    const options = {
      global: {
        headers: {
          Authorization: `Bearer ${supabaseAccessToken}`,
        },
      },
      realtime: {
        headers: {
          Authorization: `Bearer ${supabaseAccessToken}`,
        },
        // Add these options to improve realtime connection
        params: {
          apikey: supabaseAnonKey,
          eventsPerSecond: 10,
        }
      },
      auth: {
        // Ensure the JWT is used for auth as well
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: false
      }
    };

    authClientInstances.set(
      supabaseAccessToken,
      createClient<Database>(supabaseUrl, supabaseAnonKey, options)
    );
  }

  return authClientInstances.get(supabaseAccessToken)!;
};

/**
 * Helper function to check if a JWT token is expired or about to expire
 * @param token The JWT token to check
 * @param bufferSeconds Number of seconds before expiration to consider the token as expired
 * @returns True if the token is expired or about to expire, false otherwise
 */
function isTokenExpiredOrClose(token: string, bufferSeconds = 300): boolean {
  try {
    // Parse the token to get the expiration time
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) return true; // Invalid token format

    const payload = JSON.parse(atob(tokenParts[1]));
    if (!payload.exp) return true; // No expiration claim

    // Check if the token is expired or about to expire
    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    // Return true if the token is expired or will expire within the buffer time
    return timeUntilExpiration < (bufferSeconds * 1000);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired on error
  }
}

/**
 * Function to refresh the Supabase JWT token
 * @returns A promise that resolves to the new token or null if refresh failed
 */
async function refreshSupabaseToken(): Promise<string | null> {
  try {
    const response = await fetch('/api/auth/refresh-token');
    if (!response.ok) {
      throw new Error(`Failed to refresh token: ${response.statusText}`);
    }

    const data = await response.json();
    return data.token || null;
  } catch (error) {
    console.error('Error refreshing Supabase token:', error);
    return null;
  }
}

/**
 * Hook to get an authenticated Supabase client using NextAuth session
 */
export function useSupabaseClient() {
  const { data: session, update: updateSession } = useSession();
  const [supabase, setSupabase] = useState(() => createSupabaseClient());
  const previousTokenRef = useRef<string | null>(null);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to update the client with a new token
  const updateClientWithToken = useCallback(async (token: string) => {
    const client = createSupabaseClient(token);

    // Set up the realtime access token as a function that returns the current token
    if (client.realtime) {
      console.log('Setting up dynamic realtime access token');
      // This function will be called by Supabase when it needs the token
      client.realtime.accessToken = () => Promise.resolve(token);
    }

    // Update the auth session
    client.auth.setSession({
      access_token: token,
      refresh_token: '', // Next Auth handles refresh
    });

    setSupabase(client);
    return client;
  }, []);

  // Function to handle token refresh
  const handleTokenRefresh = useCallback(async () => {
    console.log('Refreshing Supabase token');
    const newToken = await refreshSupabaseToken();

    if (newToken) {
      console.log('Token refreshed successfully');
      // Update the session with the new token
      await updateSession({ supabaseAccessToken: newToken });
      previousTokenRef.current = newToken;

      // Update the client with the new token
      await updateClientWithToken(newToken);

      // Schedule the next refresh
      scheduleTokenRefresh(newToken);
    } else {
      console.error('Failed to refresh token');
    }
  }, [updateSession, updateClientWithToken]);

  // Function to schedule token refresh
  const scheduleTokenRefresh = useCallback((token: string) => {
    // Clear any existing timer
    if (refreshTimerRef.current) {
      clearTimeout(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }

    try {
      // Parse the token to get the expiration time
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) return; // Invalid token format

      const payload = JSON.parse(atob(tokenParts[1]));
      if (!payload.exp) return; // No expiration claim

      // Calculate when to refresh (5 minutes before expiration)
      const expirationTime = payload.exp * 1000; // Convert to milliseconds
      const refreshTime = expirationTime - (5 * 60 * 1000); // 5 minutes before expiration
      const timeUntilRefresh = Math.max(0, refreshTime - Date.now());

      console.log(`Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000)} seconds`);

      // Schedule the refresh
      refreshTimerRef.current = setTimeout(() => {
        handleTokenRefresh();
      }, timeUntilRefresh);
    } catch (error) {
      console.error('Error scheduling token refresh:', error);
    }
  }, [handleTokenRefresh]);

  // Effect to initialize the client when the session changes
  useEffect(() => {
    if (!session?.supabaseAccessToken) return;

    // Check if we need to update the client (token changed or about to expire)
    const tokenChanged = session.supabaseAccessToken !== previousTokenRef.current;
    const tokenExpiring = isTokenExpiredOrClose(session.supabaseAccessToken);

    if (tokenChanged || tokenExpiring) {
      if (tokenExpiring && !tokenChanged) {
        // Token is about to expire but hasn't changed yet, refresh it
        handleTokenRefresh();
      } else {
        // Token has changed, update the client
        console.log('Token changed, updating client');
        previousTokenRef.current = session.supabaseAccessToken;
        updateClientWithToken(session.supabaseAccessToken);

        // Schedule refresh for this token
        scheduleTokenRefresh(session.supabaseAccessToken);
      }
    }
  }, [session, updateClientWithToken, handleTokenRefresh, scheduleTokenRefresh]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
    };
  }, []);

  return supabase;
}