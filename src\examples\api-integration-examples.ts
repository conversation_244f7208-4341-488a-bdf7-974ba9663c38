/**
 * EXAMPLE INTEGRATIONS FOR USAGE TRACKING
 *
 * These examples show how to integrate usage tracking and limit enforcement
 * into your existing API endpoints. Copy and adapt these patterns for your
 * actual API routes.
 *
 * CURRENT SYSTEM STATUS:
 * - Messages: Limited and tracked (monthly reset)
 * - Worlds: Limited and tracked for weekly generation (weekly reset)
 * - Characters: No tracking or limits (unlimited)
 * - Refresh dates: Displayed in UI components for user awareness
 */

import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession } from '@/lib/auth-helpers';
import { withSubscriptionLimitAPI } from '@/lib/subscription-middleware';
import { checkUsageLimit, incrementUsage } from '@/lib/usage-tracking.service';
import { SubscriptionLimitError } from '@/types/subscription.types';

// ============================================================================
// EXAMPLE 1: World Creation API (No Limits - Display Tracking Only)
// ============================================================================

/**
 * Example: POST /api/v1/worlds
 * Creates a new world with optional usage tracking for display purposes
 * NOTE: Worlds are unlimited in the current system, but tracked for UI display
 */
export async function createWorldExample(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Your existing world creation logic here
    const worldData = {
      name: body.name,
      description: body.description,
      user_id: session.user.id,
      // ... other world properties
    };

    // Create world in database (your existing logic)
    // const { data: newWorld, error } = await supabaseAdmin
    //   .from('worlds')
    //   .insert(worldData)
    //   .select()
    //   .single();

    // Optional: Increment world count for display tracking
    // This is for UI display purposes only - no limits are enforced
    await incrementUsage(session.user.id, 'worlds');

    // Return success response
    return NextResponse.json({
      // world: newWorld,
      message: 'World created successfully'
    });
  } catch (error) {
    console.error('Error creating world:', error);
    return NextResponse.json(
      { error: 'Failed to create world' },
      { status: 500 }
    );
  }
}

// ============================================================================
// EXAMPLE 3: AI Message API with Usage Tracking (ENFORCED LIMITS)
// ============================================================================

/**
 * Example: POST /api/v1/ai/message
 * Sends AI message with monthly message limit enforcement
 * NOTE: This is the ONLY API that enforces limits in the current system
 */
export const sendAIMessageExample = withSubscriptionLimitAPI(
  async (request: NextRequest): Promise<NextResponse> => {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Your existing AI message logic here
    const messageData = {
      content: body.message,
      user_id: session.user.id,
      world_id: body.world_id,
      // ... other message properties
    };

    // Process AI message (your existing logic)
    // const aiResponse = await processAIMessage(messageData);

    return NextResponse.json({
      // response: aiResponse,
      message: 'AI message processed successfully',
      // Include usage info in response for UI updates
      usage: {
        // This will be populated by the middleware
        current: 0, // Will be updated after increment
        limit: 0,   // Will be set based on subscription tier
        remaining: 0,
        resetDate: new Date().toISOString() // Next monthly reset
      }
    });
  },
  async (request: NextRequest) => {
    const session = await requireUserSession(request);
    return {
      userId: session.user.id,
      limitType: 'messages' as const
    };
  }
);

// ============================================================================
// EXAMPLE 4: Manual Message Usage Tracking (Alternative Approach)
// ============================================================================

/**
 * Example: Manual message usage tracking for more complex scenarios
 * Use this approach when you need more control over the tracking logic
 * NOTE: Only use this for message limits - worlds/characters are unlimited
 */
export async function manualMessageTrackingExample(request: NextRequest) {
  try {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Check message limit before proceeding
    const limitCheck = await checkUsageLimit({
      userId: session.user.id,
      limitType: 'messages',
      increment: false
    });

    if (!limitCheck.allowed) {
      return NextResponse.json(
        {
          error: 'Message limit exceeded',
          details: {
            current: limitCheck.current,
            limit: limitCheck.limit,
            remaining: limitCheck.remaining,
            resetDate: limitCheck.resetDate, // When the limit resets
            tier: session.user.subscription_tier || 'free'
          }
        },
        { status: 429 }
      );
    }

    // Proceed with your AI message processing logic
    // const aiResponse = await processAIMessage({
    //   content: body.message,
    //   user_id: session.user.id,
    //   world_id: body.world_id
    // });

    // Increment usage after successful operation
    await incrementUsage(session.user.id, 'messages');

    return NextResponse.json({
      success: true,
      // response: aiResponse,
      usage: {
        current: limitCheck.current + 1,
        limit: limitCheck.limit,
        remaining: limitCheck.remaining - 1,
        resetDate: limitCheck.resetDate
      }
    });
  } catch (error) {
    if (error instanceof SubscriptionLimitError) {
      return NextResponse.json(
        {
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
          details: {
            limitType: error.limitType,
            current: error.current,
            limit: error.limit,
            tier: error.tier
          }
        },
        { status: 429 }
      );
    }
    throw error;
  }
}

// ============================================================================
// EXAMPLE 5: Deletion APIs (No Usage Tracking Needed)
// ============================================================================

/**
 * Example: DELETE /api/v1/worlds/[worldId]
 * Deletes a world - no usage tracking needed since worlds are unlimited
 * NOTE: World tracking is optional and for display purposes only
 */
export async function deleteWorldExample(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { worldId } = params;

    // Your existing deletion logic here
    // const { error } = await supabaseAdmin
    //   .from('worlds')
    //   .delete()
    //   .eq('id', worldId)
    //   .eq('user_id', session.user.id);

    // Optional: Update world count for display tracking
    // This is not required since worlds are unlimited
    // You could decrement the weekly counter if you want accurate display counts

    return NextResponse.json({ message: 'World deleted successfully' });
  } catch (error) {
    console.error('Error deleting world:', error);
    return NextResponse.json(
      { error: 'Failed to delete world' },
      { status: 500 }
    );
  }
}

/**
 * Example: DELETE /api/v1/characters/[characterId]
 * Deletes a character - no usage tracking needed since characters are unlimited
 */
export async function deleteCharacterExample(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { characterId } = params;

    // Your existing deletion logic here
    // const { error } = await supabaseAdmin
    //   .from('characters')
    //   .delete()
    //   .eq('id', characterId)
    //   .eq('user_id', session.user.id);

    // No usage tracking needed - characters are unlimited

    return NextResponse.json({ message: 'Character deleted successfully' });
  } catch (error) {
    console.error('Error deleting character:', error);
    return NextResponse.json(
      { error: 'Failed to delete character' },
      { status: 500 }
    );
  }
}

// ============================================================================
// INTEGRATION CHECKLIST - CURRENT SYSTEM
// ============================================================================

/**
 * INTEGRATION CHECKLIST FOR CURRENT SUBSCRIPTION SYSTEM:
 *
 * 1. For MESSAGE/AI operations (ENFORCED LIMITS):
 *    - Use withSubscriptionLimitAPI wrapper OR manual checkUsageLimit
 *    - Increment usage after successful AI processing
 *    - Return 429 status with upgrade message on limit exceeded
 *    - Include resetDate in error responses for UI display
 *
 * 2. For WORLD operations (DISPLAY TRACKING ONLY):
 *    - Optional: Call incrementUsage for display counter accuracy
 *    - No limit enforcement needed - worlds are unlimited
 *    - Weekly reset for display purposes only
 *
 * 3. For CHARACTER operations (NO TRACKING):
 *    - No usage tracking needed - characters are unlimited
 *    - Simple CRUD operations without limit checks
 *
 * 4. For UI components:
 *    - Show usage indicators with refresh dates
 *    - Display limit warnings only for messages
 *    - Provide upgrade suggestions when message limits are reached
 *    - Use refresh date display for user awareness
 *
 * 5. Error handling:
 *    - Catch SubscriptionLimitError for message APIs only
 *    - Include resetDate in error responses
 *    - Log usage tracking errors but don't fail the main operation
 *
 * 6. Database migration:
 *    - Run the usage tracking migration SQL (includes worlds_this_week)
 *    - Initialize usage records for existing users
 *    - Ensure resetDate fields are populated for UI display
 *
 * 7. Refresh Date Display:
 *    - Messages: Show monthly reset date
 *    - Worlds: Show weekly reset date (display only)
 *    - Format dates appropriately in UI components
 */
