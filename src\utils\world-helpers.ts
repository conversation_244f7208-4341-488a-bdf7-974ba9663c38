/**
 * Utilities for working with World data
 */
import { World, WorldDescription } from "@/types/world.types";

/**
 * Extract a theme or setting description from a world object
 */
export function extractDescription(world: World): string {
  if (!world.description) return world.theme || 'No theme';
  
  try {
    const description = world.description as WorldDescription;
    if (
      description.campaign_overview &&
      typeof description.campaign_overview === 'object' &&
      description.campaign_overview !== null
    ) {
      const setting = description.campaign_overview.setting;
      if (typeof setting === 'string') {
        return setting.length > 60 ? setting.substring(0, 60) + '...' : setting;
      }
    }
  } catch (error) {
    // If parsing fails, just return the theme
    console.warn('Failed to parse world description:', error);
  }
  
  // Fallback: ensure we only return a string
  if (typeof world.theme === 'string') {
    return world.theme;
  }
  return 'No description';
}

/**
 * Get the world creation date, handling different field names
 */
export function getWorldCreationDate(world: World): Date | null {
  const dateStr = world.createdAt || world.created_at;
  if (!dateStr) return null;
  
  try {
    return new Date(dateStr);
  } catch (e) {
    console.warn('Invalid date format:', dateStr);
    return null;
  }
}

/**
 * Format the world creation date as a localized string
 */
export function formatWorldCreationDate(world: World): string {
  const date = getWorldCreationDate(world);
  if (!date) return 'Unknown date';
  
  return date.toLocaleDateString();
}

/**
 * Get the world status with a default fallback to 'completed'
 */
export function getWorldStatus(world: World): 'generating' | 'completed' {
  return world.status || 'completed';
}

/**
 * Check if a world is still being generated
 */
export function isWorldGenerating(world: World): boolean {
  return getWorldStatus(world) === 'generating';
}

/**
 * Parse the world description JSON safely
 */
export function parseWorldDescription(world: World): WorldDescription | null {
  if (!world.description) return null;
  try {
    return world.description as WorldDescription;
  } catch (error) {
    console.warn('Failed to parse world description:', error);
    return null;
  }
} 