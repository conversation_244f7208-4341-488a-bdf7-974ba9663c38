# Active Context: SoloQuest

## Current Focus

*   Refining Memory Bank documentation based on code exploration (`layout.tsx`, `page.tsx`, `Header.tsx`, `Providers.tsx`, `globals.css`, `api/debug/supabase-test/route.ts`, `lib/env.ts`, `lib/auth-helpers.ts`, `lib/supabase-admin.ts`, `lib/user-service.ts`, `lib/world-service.ts`, `lib/character-service.ts`).

*   Implementing character detail pre-fetching on the world campaign page.

## Recent Changes

*   Updated `dnd-ai-app/src/app/api/v1/characters/[characterId]/route.ts`:
    *   Modified the `GET` handler's authorization logic for character details (admin or world owner).
*   Updated `systemPatterns.md` based on findings of this project.
*   Reviewed NextAuth backend configuration (`authOptions`) in `[...nextauth]/route.ts`: Google OAuth provider, custom sign-in page, JWT/session callbacks sync user data with Supa<PERSON>, UUID management, robust error handling, and debug logging.

## Next Steps (Immediate)

*   Continue to update the Memory Bank as new patterns, issues, or architectural decisions arise.

## Active Decisions & Considerations

*   Memory Bank content is being actively refined based on code exploration.
*   Confirmed core technologies and project focus.
*   Understood layout, landing page, and header structure/functionality.
*   Confirmed core technologies, project focus, layout, landing page, header functionality, and client-side auth setup.
*   Identified delegation of AI world generation to an external API.
*   Identified patterns: client components, `useSession`, `framer-motion`, centralized/validated env vars, global Providers (`SessionProvider`), detailed CSS theming, client-side `fetch` to internal API routes, dedicated Supabase admin client, server-side auth helpers, service layer using admin client, delegation to external API.
*   Still need details on: Specific AI implementation (in external service), DB schema details, NextAuth backend configuration (`authOptions`), client-side Supabase client setup, API route implementations.

## Important Patterns & Preferences (Initial Observations)

*   Use of TypeScript for type safety.
*   Modular structure (API, UI components, services, lib).
*   RESTful API design.
*   Reliance on Supabase.
*   Next.js App Router.
*   Global layout structure (Header, Footer, Providers).
*   Server-side environment variable validation.
*   Use of client components (`'use client'`) for interactivity and session management.
*   Use of `framer-motion` for animations.
*   Detailed theming strategy in `globals.css`.
*   Centralized environment variable management (`lib/env.ts`).
*   Server-side authorization helpers (`lib/auth-helpers.ts`).
*   Dedicated Supabase admin client (`lib/supabase-admin.ts`).
*   Service layer pattern (`lib/*-service.ts`) abstracting DB operations using the admin client.
*   Delegation of complex/AI tasks to external APIs (`INTERNAL_API_URL`).
*   Client-side data fetching via internal API routes pattern.

## Learnings & Insights

*   The service layer (`user`, `world`, `character`) consistently uses the `supabaseAdmin` client, suggesting most core data manipulation happens server-side with elevated privileges, relying on API route authorization (`auth-helpers`) for security.
