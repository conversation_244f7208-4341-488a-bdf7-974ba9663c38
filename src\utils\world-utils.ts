/**
 * Saves the last played world ID to localStorage
 * @param worldId The ID of the world being played
 */
export const saveLastPlayedWorld = (worldId: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('lastPlayedWorldId', worldId)
  }
}

/**
 * Gets the last played world ID from localStorage
 * @returns The ID of the last played world, or null if none
 */
export const getLastPlayedWorld = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('lastPlayedWorldId')
  }
  return null
} 