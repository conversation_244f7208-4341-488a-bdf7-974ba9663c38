# SoloQuestI Application

A Next.js application for Dungeons & Dragons players, featuring world creation, campaign management, and AI-powered gameplay assistance.

## Features

- Google Authentication
- Dark Fantasy UI
- World Creation
- Campaign Management
- Player Characters
- AI-powered DM assistance

## Tech Stack

- Next.js 15
- TypeScript
- Tailwind CSS
- NextAuth.js
- Supabase

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Google OAuth credentials

### Installation

1. Clone the repository
2. Install dependencies:

```bash
yarn install
```

3. Set up environment variables by copying the example file:

```bash
cp .env.example .env
```

1. Fill in the environment variables in `.env`:

```
# NextAuth configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# Google OAuth credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Supabase JWT Secret for Row Level Security
# Get this from your Supabase dashboard: Settings > API > JWT Settings
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Internal API URL
INTERNAL_API_URL=http://localhost:3000/api
```

### Setting up Supabase

1. Create a new project in [Supabase](https://supabase.com)
2. Get your project URL and anon key from the project settings
3. Add them to your `.env` file
4. Set up the database schema:
   - Navigate to the SQL Editor in Supabase dashboard
   - Run the SQL schema in `supabase/schema.sql`

### Google Authentication Setup

1. Create a project in [Google Cloud Console](https://console.cloud.google.com/)
2. Go to APIs & Services > Credentials
3. Create an OAuth client ID
4. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - Your production URL (when deployed)
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (for development)
   - `https://your-domain.com/api/auth/callback/google` (for production)
6. Copy the Client ID and Client Secret to your `.env.local` file

### Running the Development Server

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `app/` - Next.js app directory
  - `api/` - API routes
  - `campaign/` - Campaign management pages
  - `profile/` - User profile pages
  - `worlds/` - World creation pages
- `components/` - Reusable React components
- `lib/` - Utility functions and services
  - `supabase.ts` - Supabase client
  - `user-service.ts` - User data operations
  - `world-service.ts` - World data operations
  - `character-service.ts` - Character data operations
- `public/` - Static assets
- `supabase/` - Supabase setup files
  - `schema.sql` - Database schema

## Database Schema

The application uses the following database tables:

- `users` - User accounts
- `worlds` - DND worlds created by users
- `characters` - Player characters
- `campaigns` - Campaigns in specific worlds
- `messages` - Campaign messages between users and AI

## World Creation API Integration

The world creation feature is designed to connect with an external API. To integrate with your backend:

1. Update the API endpoint in `src/app/world-creation/page.tsx`
2. Ensure your API accepts the user ID and world creation form data
3. When implementing the actual API, replace the mock responses with real API calls


## Acknowledgements

- Icons and design elements inspired by D&D and fantasy aesthetics
- Built with Next.js App Router and Tailwind CSS