### **Website Architecture**

#### **Backend**
- User authentication and credential verification (e.g., checking world ownership).
  - **Details:** Implement OAuth 2.0 for secure authentication and role-based access control for different user types (e.g., player, admin).
- Handling requests to the internal API for data generation.
  - **Details:** Use RESTful API design principles with endpoints for world creation, character management, and campaign progression.
- Core functions for retrieving and managing frontend data and user profiles.
  - **Details:** Implement caching strategies to improve data retrieval speeds and reduce server load.

#### **Frontend**
- **Main Page:** Introduction to the project and navigation bar with sign-in functionality.
  - **Details:** Include a carousel of featured campaigns and testimonials from users.
- **World List Page:**
  - Displays user-created worlds.
  - Provides details and links to world creation.
  - Show subscription type and usage.
  - **Details:** Include filters and sorting options for easy navigation through user worlds.
- **World Creation Page:**
  - User input for world preferences (themes, difficulty, character types, DM style, etc.).
  - **Details:** Interactive form with tooltips and examples to guide users through the creation process.
  - Use "..." animation to handle generation waiting time.
- **Campaign Page:**
  - Message-based interface for interacting with the AI DM.
  - AI-generated responses and interactive storytelling.
  - Support for avatars and text-based gameplay logs.
  - Retrieve chat history.
  - Pre-set some buttons for dice rolling.
  - Use "..." animation as character typing to handle generation waiting time.
  - **Details:** Include a sidebar with quick access to inventory and character stats.
- **Profile Page:**
  - Displays user statistics, including world count and messages sent, subscription and status.
  - **Details:** Include achievements and badges for user milestones.
- **Admin Panel:**
  - Provides tracking and moderation tools for managing user-generated content.
  - **Details:** Include user management features and content approval workflows.

** USE MOCK data for now. **

#### **Internal API**
- API for world and character generation.
- API for handling AI-based messaging and storytelling.
- Workflow integration with **n8n** for message and other generation.