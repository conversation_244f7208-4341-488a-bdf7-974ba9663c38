'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import ReactMarkdown from 'react-markdown'
import CharacterInfoOverlay from '@/components/CharacterInfoOverlay'
import WorldInfoOverlay from '@/components/WorldInfoOverlay'
import { useSupabaseClient } from '@/lib/supabase-client'
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { World } from '@/types/world.types'
import { saveLastPlayedWorld } from '@/utils/world-utils'

// Message type for campaign chat
type Message = {
  id: string
  content: string
  sender: string
  world_id?: string
  user_id?: string
  character_id?: string
  timestamp: Date
  character?: string // For display purposes
}

type Character = {
  id: string
  type: 'player' | 'ai'
  name: string
  gender: string
  race: string
  class: string
  background: string
  alignment: string
  attributes: Record<string, number>
  inventory: string
  description: string
  memories: string
  user_id: string
  world_id?: string
  avatar?: string // For UI display
}

// Define type for world
type WorldData = {
  id: string
  name: string
  description: Record<string, any>
  dm_prompt: string
  notes: string
  settings: Record<string, any>
  user_id: string
  theme?: string // For UI display
  dmType?: string // For UI display
}

type WorldCampaignClientProps = {
  worldId: string
}

// Color palette for characters
const CHARACTER_COLORS = [
  'bg-blue-800 bg-opacity-30 border-blue-600',
  'bg-green-800 bg-opacity-30 border-green-600',
  'bg-purple-800 bg-opacity-30 border-purple-600',
  'bg-yellow-800 bg-opacity-30 border-yellow-600',
  'bg-pink-800 bg-opacity-30 border-pink-600',
  'bg-teal-800 bg-opacity-30 border-teal-600',
  'bg-red-800 bg-opacity-30 border-red-600',
  'bg-indigo-800 bg-opacity-30 border-indigo-600',
  'bg-orange-800 bg-opacity-30 border-orange-600',
]

// DM color and avatar
const DM_COLOR = 'bg-gray-800 bg-opacity-30 border-gray-600'
const DM_AVATAR = '/dm-avatar.png'
const DEFAULT_AVATAR = '/default-avatar.png'

export default function WorldCampaignClient({ worldId }: WorldCampaignClientProps) {
  const router = useRouter()
  const { data: session, status } = useSession()
  const supabase = useSupabaseClient()
  const subscriptionRef = useRef<RealtimeChannel | null>(null)
  const recentlySentMessagesRef = useRef<Set<string>>(new Set())

  // State management
  const [world, setWorld] = useState<WorldData | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [characters, setCharacters] = useState<Character[]>([])
  const [message, setMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null)
  const [showWorldInfo, setShowWorldInfo] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [playerCharacter, setPlayerCharacter] = useState<Character | null>(null)
  const [fetchError, setFetchError] = useState<string | null>(null)

  // Save last played world ID
  useEffect(() => {
    if (worldId) {
      saveLastPlayedWorld(worldId)
    }
  }, [worldId])

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      if (status === 'unauthenticated') {
        router.push('/signup')
        return
      }

      if (status === 'authenticated' && worldId) {
        try {
          // Fetch world data
          const worldResponse = await fetch(`/api/v1/worlds/${worldId}`)
          if (!worldResponse.ok) {
            const errorData = await worldResponse.json().catch(() => ({}))
            throw new Error(errorData.message || `Failed to fetch world (Status: ${worldResponse.status})`)
          }
          const worldData = await worldResponse.json()
          setWorld({
            ...worldData,
            description: typeof worldData.description === 'string'
              ? JSON.parse(worldData.description || '{}')
              : worldData.description
          })

          // Set characters from world data (they're included in the response)
          if (worldData.characters) {
            setCharacters(worldData.characters)
            // Find the player's character
            const userCharacter = worldData.characters.find((c: Character) => c.type === 'player' && c.user_id === session?.user?.id)
            if (userCharacter) {
              setPlayerCharacter(userCharacter)
            }
          }

          // Fetch messages
          const messagesResponse = await fetch(`/api/v1/worlds/${worldId}/messages?limit=50`)
          if (!messagesResponse.ok) throw new Error('Failed to fetch messages')
          const { messages: messageData } = await messagesResponse.json()

          // Transform messages to match our Message type
          const transformedMessages = messageData.map((msg: any) => ({
            id: msg.id.toString(),
            content: msg.content,
            sender: msg.sender,
            world_id: msg.world_id,
            user_id: msg.user_id,
            character_id: msg.character_id,
            timestamp: new Date(msg.created_at),
            character: characters.find(c => c.id === msg.character_id)?.name
          }))

          // Sort messages by timestamp (oldest first)
          transformedMessages.sort((a: Message, b: Message) => a.timestamp.getTime() - b.timestamp.getTime())

          setMessages(transformedMessages)
        } catch (error) {
          console.error('Error fetching data:', error)
          setFetchError(error instanceof Error ? error.message : 'Failed to load world data')
        }
      }
    }

    fetchData()
  }, [status, worldId, router, session?.user?.id])

  // Set up Supabase realtime subscription
  useEffect(() => {
    if (status !== 'authenticated' || !session?.user?.id || !worldId) {
      return
    }

    // Our improved supabase-client.ts handles token refreshing automatically

    // Cleanup any existing subscription
    if (subscriptionRef.current) {
      supabase.removeChannel(subscriptionRef.current)
      subscriptionRef.current = null
    }

    // Set up realtime subscription for messages from this world
    const channelName = `world-messages-${worldId}-${session.user.id.substring(0, 8)}`

    const subscription = supabase
      .channel(channelName, {
        config: {
          broadcast: { self: true },
          presence: { key: session.user.id }
        }
      })
      .on('postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `world_id=eq.${worldId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          const newMsg = payload.new as any

          // Enhanced duplicate check with recently sent message tracking
          const msgContent = newMsg.content.trim()
          const recentlySent = recentlySentMessagesRef.current.has(msgContent)

          const msgExists = recentlySent || messages.some(msg =>
            msg.id === newMsg.id.toString() ||
            (msg.content.trim() === msgContent &&
             Math.abs(new Date(msg.timestamp).getTime() - new Date(newMsg.created_at).getTime()) < 5000)
          )

          if (!msgExists) {
            // Transform and add the new message
            const transformedMsg: Message = {
              id: newMsg.id.toString(),
              content: newMsg.content,
              sender: newMsg.sender,
              world_id: newMsg.world_id,
              user_id: newMsg.user_id,
              character_id: newMsg.character_id,
              timestamp: new Date(newMsg.created_at),
              character: characters.find(c => c.id === newMsg.character_id)?.name
            }

            setMessages(prev => [...prev, transformedMsg])
          }

          // If it was a recently sent message, remove it from tracking after a delay
          if (recentlySent) {
            setTimeout(() => {
              recentlySentMessagesRef.current.delete(msgContent)
            }, 10000) // Clear after 10 seconds
          }
        }
      )
      .subscribe()

    // Store the subscription reference
    subscriptionRef.current = subscription

    // Cleanup on unmount
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current)
        subscriptionRef.current = null
      }
    }
  }, [worldId, session, status, supabase, messages, characters])

  // Scroll messages container to bottom when messages change
  useEffect(() => {
    const messagesContainer = messagesEndRef.current?.parentElement;
    if (messagesContainer) {
      const { scrollHeight, clientHeight } = messagesContainer;
      // Only scroll if content exceeds visible area
      if (scrollHeight > clientHeight) {
        messagesContainer.scrollTop = scrollHeight;
      }
    }
  }, [messages])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!message.trim() || !world) return

    // Track this message as recently sent to avoid duplicates
    recentlySentMessagesRef.current.add(message.trim())

    // Add player message with proper character details
    const playerMessage: Message = {
      id: Date.now().toString(),
      content: message,
      sender: 'player',
      world_id: world.id,
      timestamp: new Date(),
      character_id: playerCharacter?.id,
      character: playerCharacter?.name
    }

    setMessages((prev) => [...prev, playerMessage])
    setMessage('')
    setIsTyping(true)

    try {
      // Send message to API
      const response = await fetch(`/api/v1/worlds/${world.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: message
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      // The AI response will come through the realtime subscription
      // Set a timeout to hide the typing indicator if no response comes
      // within a reasonable time (15 seconds)
      const typingTimeout = setTimeout(() => {
        setIsTyping(false)
      }, 15000)

      // Clear the timeout when component unmounts
      return () => clearTimeout(typingTimeout)
    } catch (error) {
      console.error('Error sending message:', error)
      setIsTyping(false)
      // Remove from recently sent if failed
      recentlySentMessagesRef.current.delete(message.trim())
      // Handle error (show toast, error state, etc.)
    }
  }

  // Function to get character color by ID
  const getCharacterColor = (characterId: string | undefined, sender: string): string => {
    if (!characterId) {
      // If it's DM message
      if (sender === 'DM' || sender === 'ai') {
        return DM_COLOR;
      }
      return CHARACTER_COLORS[0]; // Default color
    }

    // Find character index to determine color
    const characterIndex = characters.findIndex(c => c.id === characterId);
    if (characterIndex === -1) return CHARACTER_COLORS[0];

    // Cycle through available colors
    return CHARACTER_COLORS[characterIndex % CHARACTER_COLORS.length];
  }

  // Function to check if message is from a player character
  const isPlayerCharacter = (characterId: string | undefined): boolean => {
    if (!characterId) return false;
    const character = characters.find(c => c.id === characterId);
    return character?.type === 'player';
  }

  // Function to determine display name for messages
  const getMessageDisplayName = (msg: Message): string => {
    if (msg.sender === 'player' && msg.character) {
      return msg.character
    }

    const character = characters.find(c => c.id === msg.character_id)
    if (character) {
      return character.name
    }

    if (msg.sender === 'DM' || msg.sender === 'ai') {
      return 'Game Master'
    }

    return playerCharacter?.name || 'You'
  }

  // Function to get avatar URL for a message or character
  const getAvatarUrl = (characterId: string | undefined, sender: string): string => {
    // If it's a DM/AI message
    if (sender === 'DM' || sender === 'ai') {
      return DM_AVATAR;
    }

    // If it's a player message
    if (sender === 'player') {
      return playerCharacter?.avatar || DEFAULT_AVATAR;
    }

    // Try to find the character's avatar
    if (characterId) {
      const character = characters.find(c => c.id === characterId);
      if (character?.avatar) {
        return character.avatar;
      }
    }

    // Default fallback
    return DEFAULT_AVATAR;
  }

  // Loading state
  if (status === 'loading' || (!world && !fetchError)) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    )
  }

  // Error state
  if (fetchError) {
    return (
      <div className="max-w-lg mx-auto my-12 fantasy-card">
        <div className="fantasy-card-header bg-red-900">
          <h2 className="text-xl font-bold">Error Loading World</h2>
        </div>
        <div className="p-6 flex flex-col items-center">
          <div className="mb-4 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className="text-red-300 mb-4">{fetchError}</p>
            <p className="text-gray-400 mb-6">The world could not be loaded. Please try again later or return to your worlds.</p>
          </div>
          <button
            onClick={() => router.push('/worlds')}
            className="fantasy-button px-8 py-3"
          >
            Return to Worlds
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Sidebar with party members and world info */}
        <div className="md:col-span-1">
          <div
            className="fantasy-card mb-6 cursor-pointer hover:border-primary transition-colors duration-200"
            onClick={() => setShowWorldInfo(true)}
          >
            <div className="fantasy-card-header">
              <h2 className="text-xl font-bold">World: {world?.name}</h2>
            </div>
            <div className="p-4 text-sm text-text-secondary">
              <div className="mt-2 flex space-x-2">
                <span className="px-2 py-1 bg-primary bg-opacity-20 border border-primary border-opacity-30 rounded-full text-xs">
                  {world?.theme || 'Default Theme'}
                </span>
                {world?.settings?.dmType && world?.settings.dmType !== 'normal' && (
                  <span className="px-2 py-1 bg-secondary bg-opacity-20 border border-secondary border-opacity-30 rounded-full text-xs">
                    {`${world?.settings.dmType.charAt(0).toUpperCase()}${world?.settings.dmType.slice(1)} DM`}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="fantasy-card">
            <div className="fantasy-card-header">
              <h2 className="text-xl font-bold">Your Party</h2>
            </div>
            <div className="p-4">
              {/* Your Character Section */}
              {playerCharacter && (
                <>
                  <div className="mb-3 px-2 py-1 text-sm font-semibold border-b border-gray-700">
                    You
                  </div>
                  <div className="mb-4">
                    <div
                      onClick={() => setSelectedCharacter(playerCharacter)}
                      className={`flex items-center p-2 rounded-md cursor-pointer transition-colors duration-200 border border-opacity-30 ${getCharacterColor(playerCharacter.id, 'player')}`}
                    >
                      <div className="w-10 h-10 relative mr-3">
                        <Image
                          src={playerCharacter.avatar || '/default-avatar.png'}
                          alt={playerCharacter.name}
                          width={40}
                          height={40}
                          className="rounded-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{playerCharacter.name}</h3>
                        <p className="text-xs text-text-secondary">{playerCharacter.race} - {playerCharacter.class}</p>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* AI Characters Section */}
              <div className="mb-3 px-2 py-1 text-sm font-semibold border-b border-gray-700">
                AI Characters
              </div>
              <div className="space-y-3">
                {characters
                  .filter(character => character.id !== playerCharacter?.id)
                  .map((character) => {
                    const colorClass = getCharacterColor(character.id, character.type === 'player' ? 'player' : 'ai');

                    return (
                      <div
                        key={character.id}
                        onClick={() => setSelectedCharacter(character)}
                        className={`flex items-center p-2 rounded-md cursor-pointer transition-colors duration-200 ${
                          character.type === "player"
                            ? 'border border-opacity-30'
                            : 'hover:bg-background-darker'
                        } ${colorClass}`}
                      >
                        <div className="w-10 h-10 relative mr-3">
                          <Image
                            src={character.avatar || '/default-avatar.png'}
                            alt={character.name}
                            width={40}
                            height={40}
                            className="rounded-full object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium text-sm">{character.name}</h3>
                          <p className="text-xs text-text-secondary">{character.race} - {character.class}</p>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 text-xs text-text-secondary">
            <p className="mb-1">
              <span className="font-bold">AI Game Master:</span> Narratives are generated by an AI game master.
            </p>
            <p className="mb-1">
              <span className="font-bold">AI Character:</span> Ai characters will act as an actual player.
            </p>
          </div>
        </div>

        {/* Main campaign chat area */}
        <div className="md:col-span-3 fantasy-card flex flex-col h-[85vh]">
          <div className="fantasy-card-header">
            <h2 className="text-xl font-bold">Adventure Log</h2>
          </div>

          {/* Messages Container */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
            {messages.map((msg) => {
              const isPlayer = isPlayerCharacter(msg.character_id) || msg.sender === 'player';
              const colorClass = getCharacterColor(msg.character_id, msg.sender);
              const avatarUrl = getAvatarUrl(msg.character_id, msg.sender);
              const displayName = getMessageDisplayName(msg);

              return (
                <div key={msg.id} className="space-y-1">
                  {/* Avatar and name row */}
                  <div className={`flex items-center ${isPlayer ? 'justify-end' : 'justify-start'}`}>
                    {!isPlayer && (
                      <div className="w-8 h-8 relative mr-2 flex-shrink-0">
                        <Image
                          src={avatarUrl}
                          alt={displayName}
                          width={32}
                          height={32}
                          className="rounded-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = DEFAULT_AVATAR;
                          }}
                        />
                      </div>
                    )}

                    <div className="text-sm font-medium">{displayName}</div>

                    {isPlayer && (
                      <div className="w-8 h-8 relative ml-2 flex-shrink-0">
                        <Image
                          src={avatarUrl}
                          alt={displayName}
                          width={32}
                          height={32}
                          className="rounded-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = DEFAULT_AVATAR;
                          }}
                        />
                      </div>
                    )}
                  </div>

                  {/* Message content */}
                  <div className={`flex ${isPlayer ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] p-3 rounded-lg border ${colorClass} ${isPlayer ? 'rounded-tr-none' : 'rounded-tl-none'}`}>
                      <div className="prose prose-sm prose-invert max-w-none">
                        <ReactMarkdown>
                          {msg.content}
                        </ReactMarkdown>
                      </div>

                      <div className="text-xs text-text-secondary mt-1">
                        {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            {isTyping && (
              <div className="space-y-1">
                {/* Avatar and name row */}
                <div className="flex items-center justify-start">
                  <div className="w-8 h-8 relative mr-2 flex-shrink-0">
                    <Image
                      src={DM_AVATAR}
                      alt="Game Master"
                      width={32}
                      height={32}
                      className="rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = DEFAULT_AVATAR;
                      }}
                    />
                  </div>

                  <div className="text-sm font-medium">Game Master</div>
                </div>

                {/* Typing indicator */}
                <div className="flex justify-start">
                  <div className={`p-3 rounded-lg border rounded-tl-none ${DM_COLOR}`}>
                    <div className="loading-dots">
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-gray-800">
            <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="What would you like to do?"
                className="fantasy-input flex-1"
              />
              <button
                type="submit"
                disabled={!message.trim() || isTyping}
                className="fantasy-button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </form>
          </div>
        </div>
      </div>

      {selectedCharacter && (
        <CharacterInfoOverlay
          character={selectedCharacter}
          onClose={() => setSelectedCharacter(null)}
        />
      )}

      {showWorldInfo && world && (
        <WorldInfoOverlay
          world={world as World}
          onClose={() => setShowWorldInfo(false)}
        />
      )}
    </>
  )
}





