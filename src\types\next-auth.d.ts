import NextAuth from "next-auth"
import { SubscriptionTier } from "./schema.types"

declare module "next-auth" {
  /**
   * Extends the built-in User type with additional properties
   */
  interface User {
    id: string
    subscription_tier?: SubscriptionTier
  }

  /**
   * Extends the built-in Session type with additional properties
   */
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      subscription_tier?: SubscriptionTier
    }
    /** Supabase JWT token for client-side authenticated requests */
    supabaseAccessToken?: string
    accessToken?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    dbUserId?: string
    subscription_tier?: SubscriptionTier
  }
}