import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireAdmin } from '@/lib/auth-helpers';
import type { Character } from '@/types/schema.types';

/**
 * GET /api/v1/characters
 * List all characters (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await requireUserSession(request);
    await requireAdmin(session);

    // Get all characters from Supabase using admin client to bypass RLS
    const { data, error, count } = await supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact' });
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({
      characters: data as Character[],
      count
    });
  } catch (error) {
    console.error('Error fetching characters:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
