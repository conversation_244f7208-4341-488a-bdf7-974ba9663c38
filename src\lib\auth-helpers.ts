import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { supabaseAdmin } from './supabase-admin';

/**
 * Ensures the request has a valid authenticated user session.
 * Throws a Response with 401 status if unauthorized.
 */
export async function requireUserSession(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    throw NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  return session;
}

/**
 * Checks if the session user is an admin.
 * Throws a Response with 403 status if not.
 */
export async function requireAdmin(session: any) {
  const { data: userData, error } = await supabaseAdmin
    .from('users')
    .select('is_admin')
    .eq('id', session.user.id)
    .single();

  if (error || !userData?.is_admin) {
    throw NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
}

/**
 * Checks if the session user is the owner of the world or an admin.
 * Throws a Response with 403 status if forbidden.
 * Returns the world data if authorized.
 */
export async function requireWorldOwnerOrAdmin(session: any, worldId: string) {
  // Fetch the world
  const { data: world, error: worldError } = await supabaseAdmin
    .from('worlds')
    .select('*')
    .eq('id', worldId)
    .single();

  if (worldError) {
    if (worldError.code === 'PGRST116') {
      throw NextResponse.json({ error: 'World not found' }, { status: 404 });
    }
    throw NextResponse.json({ error: worldError.message }, { status: 500 });
  }

  // Fetch user admin status
  const { data: userData, error: userError } = await supabaseAdmin
    .from('users')
    .select('is_admin')
    .eq('id', session.user.id)
    .single();

  if (userError) {
    throw NextResponse.json({ error: userError.message }, { status: 500 });
  }

  if (!userData?.is_admin && world.user_id !== session.user.id) {
    throw NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  return world;
}
/**
 * Checks if the session user is the target user or an admin.
 * Throws a Response with 403 status if forbidden.
 */
export async function requireSelfOrAdmin(session: any, targetUserId: string) {
  if (session.user.id === targetUserId) {
    return;
  }

  const { data: userData, error } = await supabaseAdmin
    .from('users')
    .select('is_admin')
    .eq('id', session.user.id)
    .single();

  if (error || !userData?.is_admin) {
    throw NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
}
