# Project Brief: SoloQuest

## Core Goal

To develop "SoloQuest", an AI-powered web application designed for a single-player Dungeons & Dragons (or similar TTRPG) experience, focusing on immersive storytelling.

## Key Features (Inferred)

*   User authentication and profiles.
*   Character creation and management.
*   World/Campaign creation and management.
*   AI-driven gameplay assistance and storytelling.
*   API for managing game entities (characters, worlds/campaigns, users, stats).

## Scope

The project involves building a full-stack web application using Next.js, TypeScript, Supabase, and AI integrations. It includes frontend user interfaces for gameplay and management, and a backend API. The focus is on a single-player experience.

## Initial State

This document marks the beginning of the Memory Bank creation. Further details will be populated as the project context is explored.
