import { useState } from 'react'
import { WorldCreationForm } from '@/types/world.types'

export function useWorldForm() {
  const [formState, setFormState] = useState<WorldCreationForm>({
    name: '',
    theme: '',
    worldCustomization: '',
    dmType: 'normal',
    playerCharacterCustomization: '',
    aiCharacterCount: 1,
    characterCustomizations: ['', '', '', '']
  })

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target

    if (name === 'aiCharacterCount') {
      const count = Math.min(Math.max(0, parseInt(value) || 0), 3)
      setFormState(prev => ({ ...prev, [name]: count }))
    } else {
      setFormState(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleCharacterCustomizationChange = (index: number, value: string) => {
    setFormState(prev => {
      const newCustomizations = [...prev.characterCustomizations]
      newCustomizations[index] = value
      return { ...prev, characterCustomizations: newCustomizations }
    })
  }

  const resetForm = () => {
    setFormState({
      name: '',
      theme: '',
      worldCustomization: '',
      dmType: 'normal',
      playerCharacterCustomization: '',
      aiCharacterCount: 1,
      characterCustomizations: ['', '', '', '']
    })
  }

  return {
    formState,
    setFormState,
    handleInputChange,
    handleCharacterCustomizationChange,
    resetForm
  }
}
