import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireAdmin } from '@/lib/auth-helpers';

/**
 * GET /api/v1/stats
 * Get global statistics - counts of users, worlds, characters, and messages (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await requireUserSession(request);
    await requireAdmin(session);

    // Get user count
    const { count: usersCount, error: usersError } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true });
      
    if (usersError) {
      return NextResponse.json({ error: usersError.message }, { status: 500 });
    }
    
    // Get world count
    const { count: worldsCount, error: worldsError } = await supabaseAdmin
      .from('worlds')
      .select('*', { count: 'exact', head: true });
      
    if (worldsError) {
      return NextResponse.json({ error: worldsError.message }, { status: 500 });
    }

    // Get character count
    const { count: charactersCount, error: charactersError } = await supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact', head: true });
      
    if (charactersError) {
      return NextResponse.json({ error: charactersError.message }, { status: 500 });
    }

    // Get message count
    const { count: messagesCount, error: messagesError } = await supabaseAdmin
      .from('messages')
      .select('*', { count: 'exact', head: true });
      
    if (messagesError) {
      return NextResponse.json({ error: messagesError.message }, { status: 500 });
    }
    
    // Return the statistics
    return NextResponse.json({
      users_count: usersCount,
      worlds_count: worldsCount,
      characters_count: charactersCount,
      messages_count: messagesCount,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching global stats:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
