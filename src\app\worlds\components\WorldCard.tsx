import Link from 'next/link'
import { World } from '@/types/world.types'
import { extractDescription, formatWorldCreationDate, isWorldGenerating } from '@/utils/world-helpers'

interface WorldCardProps {
  world: World
}

export function WorldCard({ world }: WorldCardProps) {
  if (!world || !world.id || !world.name) {
    console.warn('Skipping invalid world:', world)
    return null
  }

  return (
    <div className="border border-gray-800 rounded-lg p-4 hover:bg-background-darker transition duration-200">
      <div className="flex justify-between items-start">
        <h3 className="text-lg font-bold text-primary">{world.name}</h3>
        <span
          className={`text-xs px-2 py-1 rounded-full ${
            isWorldGenerating(world)
              ? 'bg-amber-900/20 text-amber-400 border border-amber-900/30'
              : 'bg-green-900/20 text-green-400 border border-green-900/30'
          }`}
        >
          {isWorldGenerating(world) ? 'Generating...' : 'Ready'}
        </span>
      </div>

      <p className="text-sm text-text-secondary mt-1 line-clamp-2">
        {extractDescription(world)}
        {world.dmType && world.dmType !== 'normal' && (
          <span className="ml-2 px-2 py-0.5 bg-secondary bg-opacity-20 rounded-full text-xs">
            {world.dmType.charAt(0).toUpperCase() + world.dmType.slice(1)} DM
          </span>
        )}
      </p>

      <div className="mt-3 flex justify-between items-center">
        <span className="text-xs text-text-secondary">
          Created {formatWorldCreationDate(world)}
        </span>

        <Link
          href={`/world/${world.id}`}
          className={`text-sm font-medium text-primary hover:underline ${
            isWorldGenerating(world) ? 'opacity-50 pointer-events-none' : ''
          }`}
        >
          {isWorldGenerating(world) ? 'Preparing...' : 'Enter World →'}
        </Link>
      </div>
    </div>
  )
}
