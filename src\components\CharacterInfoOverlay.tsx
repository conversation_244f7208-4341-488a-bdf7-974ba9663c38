'use client'

import React from 'react'
import Image from 'next/image'
import ReactMarkdown from 'react-markdown'

type CharacterInfoOverlayProps = {
  character: {
    id: string
    type: 'player' | 'ai'
    name: string
    gender: string
    race: string
    class: string
    background: string
    alignment: string
    attributes: Record<string, number>
    inventory: string
    description: string
    memories: string
    avatar?: string
  }
  onClose: () => void
}

export default function CharacterInfoOverlay({ character, onClose }: CharacterInfoOverlayProps) {
  // Handle attributes that might be string or object
  const attributes = React.useMemo(() => {
    if (!character.attributes) return {}
    
    // If attributes is a string (from older data), parse it
    if (typeof character.attributes === 'string') {
      try {
        return JSON.parse(character.attributes) as Record<string, number>
      } catch {
        return {} as Record<string, number>
      }
    }
    
    // Otherwise use it directly as an object
    return character.attributes
  }, [character.attributes])

  // Close when clicking outside the modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-background-darker border border-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 relative">
                <Image 
                  src={character.avatar || '/default-avatar.png'}
                  alt={character.name}
                  width={64}
                  height={64}
                  className="rounded-full object-cover border-2 border-primary"
                />
              </div>
              <div>
                <h2 className="text-2xl font-medieval text-primary">{character.name}</h2>
                <p className="text-text-secondary">
                  {character.race} • {character.class} • {character.alignment}
                </p>
              </div>
            </div>
            <button 
              onClick={onClose}
              className="text-text-secondary hover:text-white transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Character Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-medieval text-primary mb-3">Attributes</h3>
              <div className="grid grid-cols-3 gap-3">
                {Object.entries(attributes).map(([key, value]) => (
                  <div key={key} className="bg-background p-3 rounded border border-gray-800">
                    <span className="text-text-secondary text-sm capitalize">{key}</span>
                    <div className="text-lg font-bold">{value}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medieval text-primary mb-3">Background</h3>
              <p className="text-text-secondary">{character.background}</p>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-medieval text-primary mb-3">Description</h3>
            <div className="text-text-secondary prose prose-invert prose-sm max-w-none">
              <ReactMarkdown>{character.description}</ReactMarkdown>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-medieval text-primary mb-3">Inventory</h3>
            <p className="text-text-secondary whitespace-pre-line">{character.inventory}</p>
          </div>

          {character.memories && (
            <div className="mt-8">
              <h3 className="text-lg font-medieval text-primary mb-3">Memories</h3>
              <p className="text-text-secondary whitespace-pre-line">{character.memories}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}