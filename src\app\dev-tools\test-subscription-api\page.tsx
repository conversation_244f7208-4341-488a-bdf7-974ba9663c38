'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import SubscriptionBadge from '@/components/SubscriptionBadge'
import { SubscriptionTier } from '@/types/schema.types'

export default function TestSubscriptionAPI() {
  const { data: session, status } = useSession()
  const [subscriptionInfo, setSubscriptionInfo] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [updateLoading, setUpdateLoading] = useState(false)

  const fetchSubscriptionInfo = async () => {
    if (!session?.user?.id) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/v1/users/me/subscription')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setSubscriptionInfo(data)
    } catch (err: any) {
      setError(err.message)
      console.error('Error fetching subscription info:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateSubscriptionTier = async (newTier: SubscriptionTier) => {
    if (!session?.user?.id) return

    setUpdateLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/v1/users/me/subscription', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription_tier: newTier
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setSubscriptionInfo(data)
      
      // Refresh the page to update the session
      window.location.reload()
    } catch (err: any) {
      setError(err.message)
      console.error('Error updating subscription tier:', err)
    } finally {
      setUpdateLoading(false)
    }
  }

  if (status === 'loading') {
    return <div className="container mx-auto py-8 px-4">Loading...</div>
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-medieval text-primary mb-4">Authentication Required</h1>
          <p className="text-text-secondary">Please sign in to test the subscription API.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="bg-background-darker border border-gray-800 rounded-lg p-6">
        <h1 className="text-2xl font-medieval text-primary mb-6">Subscription API Test</h1>

        <div className="space-y-6">
          {/* Current Session Info */}
          <div className="p-4 bg-background border border-gray-800 rounded-lg">
            <h2 className="text-lg font-medieval text-primary mb-3">Current Session</h2>
            <div className="space-y-2 text-sm">
              <div><strong>User ID:</strong> {session?.user?.id}</div>
              <div><strong>Email:</strong> {session?.user?.email}</div>
              <div><strong>Name:</strong> {session?.user?.name}</div>
              <div>
                <strong>Subscription Tier (from session):</strong>{' '}
                {session?.user?.subscription_tier ? (
                  <SubscriptionBadge tier={session.user.subscription_tier} size="sm" />
                ) : (
                  'Not available'
                )}
              </div>
            </div>
          </div>

          {/* Fetch Subscription Info */}
          <div className="p-4 bg-background border border-gray-800 rounded-lg">
            <h2 className="text-lg font-medieval text-primary mb-3">Fetch Subscription Info</h2>
            <button
              onClick={fetchSubscriptionInfo}
              disabled={loading}
              className="fantasy-button mb-4"
            >
              {loading ? 'Loading...' : 'Fetch Subscription Info'}
            </button>

            {subscriptionInfo && (
              <div className="mt-4 p-3 bg-background-darker border border-gray-700 rounded">
                <h3 className="font-medium mb-2">API Response:</h3>
                <pre className="text-xs text-text-secondary overflow-x-auto">
                  {JSON.stringify(subscriptionInfo, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Update Subscription Tier (Admin Only) */}
          <div className="p-4 bg-background border border-gray-800 rounded-lg">
            <h2 className="text-lg font-medieval text-primary mb-3">Update Subscription Tier</h2>
            <p className="text-text-secondary text-sm mb-4">
              Note: This requires admin privileges. Regular users cannot change their subscription tier through this API.
            </p>
            
            <div className="flex gap-2 flex-wrap">
              {(['free', 'pro', 'pro_plus'] as SubscriptionTier[]).map((tier) => (
                <button
                  key={tier}
                  onClick={() => updateSubscriptionTier(tier)}
                  disabled={updateLoading}
                  className="px-4 py-2 bg-primary/20 text-primary border border-primary/30 rounded hover:bg-primary/30 transition-colors disabled:opacity-50"
                >
                  {updateLoading ? 'Updating...' : `Set to ${tier}`}
                </button>
              ))}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
              <h3 className="text-red-400 font-medium mb-2">Error</h3>
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
