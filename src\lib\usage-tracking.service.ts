import { supabaseAdmin } from './supabase-admin';
import { getMessageLimit, getWorldsPerWeekLimit } from '@/config/subscription.config';
import {
  SubscriptionTier,
  UserUsage
} from '@/types/schema.types';
import {
  UsageCheckResult,
  UserUsageStats,
  LimitType,
  LimitCheckOptions,
  SubscriptionLimitError
} from '@/types/subscription.types';

/**
 * Initialize user usage tracking record
 */
export async function initializeUserUsage(userId: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin.rpc('initialize_user_usage', {
      p_user_id: userId
    });

    if (error) {
      console.error('Error initializing user usage:', error);
      throw new Error(`Failed to initialize user usage: ${error.message}`);
    }
  } catch (error) {
    console.error('Error in initializeUserUsage:', error);
    throw error;
  }
}

/**
 * Get current user usage statistics
 */
export async function getUserUsage(userId: string): Promise<UserUsage | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user usage:', error);
      throw new Error(`Failed to get user usage: ${error.message}`);
    }

    return data || null;
  } catch (error) {
    console.error('Error in getUserUsage:', error);
    throw error;
  }
}

/**
 * Get total world count for a user (for display purposes only - no limits enforced)
 */
export async function getUserWorldCount(userId: string): Promise<number> {
  try {
    const { count, error } = await supabaseAdmin
      .from('worlds')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (error) {
      console.error('Error getting user world count:', error);
      throw new Error(`Failed to get user world count: ${error.message}`);
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getUserWorldCount:', error);
    throw error;
  }
}

/**
 * Get total character count for a user (for display purposes only - no limits enforced)
 */
export async function getUserCharacterCount(userId: string): Promise<number> {
  try {
    const { count, error } = await supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (error) {
      console.error('Error getting user character count:', error);
      throw new Error(`Failed to get user character count: ${error.message}`);
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getUserCharacterCount:', error);
    throw error;
  }
}

/**
 * Check if user can perform an action based on their subscription limits
 */
export async function checkUsageLimit(options: LimitCheckOptions): Promise<UsageCheckResult> {
  const { userId, limitType, increment = false } = options;

  try {
    // Get user's subscription tier
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error(`Failed to get user subscription: ${userError.message}`);
    }

    const subscriptionTier = user.subscription_tier as SubscriptionTier;

    // Initialize usage if needed
    await initializeUserUsage(userId);

    if (limitType === 'messages') {
      const limit = getMessageLimit(subscriptionTier);
      const usage = await getUserUsage(userId);
      let current = usage?.messages_this_month || 0;
      const resetDate = usage?.messages_reset_date;

      // Check if we need to reset monthly count
      const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
      const resetMonth = resetDate ? resetDate.slice(0, 7) : '';
      if (resetDate && resetMonth < currentMonth) {
        // Reset will happen automatically in the database function
        current = 0;
      }

      const allowed = current < limit;

      // If incrementing and allowed, increment the usage
      if (increment && allowed) {
        const newCount = await incrementUsage(userId, limitType);
        current = newCount;
      }

      return {
        allowed,
        current,
        limit,
        remaining: Math.max(0, limit - current),
        resetDate,
        message: allowed ? undefined : `Message limit exceeded (${current}/${limit})`,
        subscriptionTier
      };
    }

    if (limitType === 'worlds') {
      const limit = getWorldsPerWeekLimit(subscriptionTier);
      const usage = await getUserUsage(userId);
      let current = usage?.worlds_this_week || 0;
      const resetDate = usage?.worlds_reset_date;

      // Check if we need to reset weekly count
      const currentWeek = getWeekStart(new Date()).toISOString().slice(0, 10); // YYYY-MM-DD format
      const resetWeek = resetDate ? getWeekStart(new Date(resetDate)).toISOString().slice(0, 10) : '';
      if (resetDate && resetWeek < currentWeek) {
        // Reset will happen automatically in the database function
        current = 0;
      }

      const allowed = current < limit;

      // If incrementing and allowed, increment the usage
      if (increment && allowed) {
        const newCount = await incrementUsage(userId, limitType);
        current = newCount;
      }

      return {
        allowed,
        current,
        limit,
        remaining: Math.max(0, limit - current),
        resetDate,
        message: allowed ? undefined : `World creation limit exceeded (${current}/${limit})`,
        subscriptionTier
      };
    }

    throw new Error(`Invalid limit type: ${limitType}`);
  } catch (error) {
    console.error('Error in checkUsageLimit:', error);
    throw error;
  }
}

/**
 * Helper function to get the start of the week (Monday)
 */
function getWeekStart(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is Sunday
  return new Date(d.setDate(diff));
}

/**
 * Increment usage counter for messages and worlds
 */
export async function incrementUsage(
  userId: string,
  limitType: LimitType
): Promise<number> {
  try {
    if (limitType === 'messages') {
      const { data: msgData, error: msgError } = await supabaseAdmin.rpc(
        'increment_message_count',
        { p_user_id: userId }
      );
      if (msgError) throw msgError;
      return msgData;
    }

    if (limitType === 'worlds') {
      const { data: worldData, error: worldError } = await supabaseAdmin.rpc(
        'increment_world_count',
        { p_user_id: userId }
      );
      if (worldError) throw worldError;
      return worldData;
    }

    throw new Error(`Invalid limit type for increment: ${limitType}`);
  } catch (error) {
    console.error('Error in incrementUsage:', error);
    throw error;
  }
}

/**
 * Note: decrementUsage is no longer needed with the simplified structure
 * Worlds and characters are counted directly from their respective tables
 * Messages are reset monthly, not decremented
 */

/**
 * Get comprehensive usage statistics for a user
 */
export async function getUserUsageStats(userId: string): Promise<UserUsageStats> {
  try {
    // Get user's subscription tier
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error(`Failed to get user subscription: ${userError.message}`);
    }

    const subscriptionTier = user.subscription_tier as SubscriptionTier;

    // Get usage stats for both messages and worlds
    const [messagesCheck, worldsCheck] = await Promise.all([
      checkUsageLimit({ userId, limitType: 'messages' }),
      checkUsageLimit({ userId, limitType: 'worlds' })
    ]);

    return {
      messages_monthly: messagesCheck,
      worlds_generation_weekly: worldsCheck,
      subscription_tier: subscriptionTier
    };
  } catch (error) {
    console.error('Error in getUserUsageStats:', error);
    throw error;
  }
}

/**
 * Check limit and throw error if exceeded
 */
export async function enforceUsageLimit(options: LimitCheckOptions): Promise<void> {
  const result = await checkUsageLimit(options);

  if (!result.allowed) {
    throw new SubscriptionLimitError(
      options.limitType,
      result.current,
      result.limit,
      result.subscriptionTier,
      result.message
    );
  }
}
