import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import type { User } from '@/types/schema.types';

/**
 * GET /api/v1/users/[userId]
 * Also handles /api/v1/users/me when [userId] is 'me'
 * Get a single user by ID or the current user if [userId] is 'me'
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Get user from Supabase
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', targetUserId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return user data
    return NextResponse.json({
      id: (user as User).id,
      email: (user as User).email,
      name: (user as User).name,
      created_at: (user as User).created_at,
      updated_at: (user as User).updated_at,
      // Include other non-sensitive fields
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/users/[userId]
 * Also handles /api/v1/users/me when [userId] is 'me'
 * Update user data
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Parse request body
    const userData = await request.json();

    // Check if the user exists
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('id', targetUserId)
      .maybeSingle();

    if (checkError) {
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Fields that are allowed to be updated
    const updateData: Record<string, any> = {};

    // Only include fields that are provided and allowed to be updated
    if (userData.name !== undefined) updateData.name = userData.name;
    // Add more fields as needed, but be careful with sensitive data

    // Don't update if no valid fields are provided
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Always update the updated_at timestamp
    updateData.updated_at = new Date().toISOString();

    // Update the user in Supabase
    const { data: updatedUser, error: updateError } = await supabaseAdmin
      .from('users')
      .update(updateData)
      .eq('id', targetUserId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    // Return the updated user data
    return NextResponse.json({
      id: (updatedUser as User).id,
      email: (updatedUser as User).email,
      name: (updatedUser as User).name,
      updated_at: (updatedUser as User).updated_at,
      // Include other non-sensitive fields
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/users/[userId]
 * Also handles /api/v1/users/me when [userId] is 'me'
 * Delete a user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Check if the user exists
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('id', targetUserId)
      .maybeSingle();

    if (checkError) {
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Delete the user
    const { error: deleteError } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', targetUserId);

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    // Return success
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
