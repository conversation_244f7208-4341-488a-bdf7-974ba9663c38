import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { generateWorld } from '@/lib/world-service';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import type { World } from '@/types/schema.types';

/**
 * GET /api/v1/users/[userId]/worlds
 * List user's worlds
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { userId } = await params;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Get all worlds for the user
    const { data: worlds, error, count } = await supabaseAdmin
      .from('worlds')
      .select('*', { count: 'exact' })
      .eq('user_id', targetUserId)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      worlds: worlds as World[],
      count
    });
  } catch (error) {
    console.error('Error fetching user worlds:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/users/[userId]/worlds
 * Create world for user
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { userId } = await params;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Parse request body for world data
    const worldData = await request.json();
    
    // Validate aiCharacterCount is required and between 0 and 3
    if (worldData.aiCharacterCount === undefined) {
      return NextResponse.json(
        { error: 'Missing required field: aiCharacterCount' },
        { status: 400 }
      );
    }
    
    const count = parseInt(worldData.aiCharacterCount);
    if (isNaN(count) || count < 0 || count > 3) {
      return NextResponse.json(
        { error: 'aiCharacterCount must be between 0 and 3' },
        { status: 400 }
      );
    }
    
    // Validate dmType if provided
    if (worldData.dmType && !['normal', 'hardcore', 'funny', 'easygoing'].includes(worldData.dmType)) {
      return NextResponse.json(
        { error: 'dmType must be one of: normal, hardcore, funny, easygoing' },
        { status: 400 }
      );
    }
    
    // If dmType is not provided, set it to normal
    if (!worldData.dmType) {
      worldData.dmType = 'normal';
    }
    
    // Process character customizations
    if (worldData.characterCustomizations) {
      // Ensure only the customizations for the selected number of characters are sent
      worldData.characterCustomizations = worldData.characterCustomizations
        .slice(0, count)
        .filter(Boolean); // Remove empty strings
    }
    
    // Process player character customization
    if (!worldData.playerCharacterCustomization) {
      worldData.playerCharacterCustomization = '';
    }
    
    // Process world name
    if (!worldData.name) {
      worldData.name = "";
    }
    
    // Instead of directly inserting into the database, use the internal API
    // to generate the world
    const newWorld = await generateWorld(targetUserId, worldData);
    
    if (!newWorld) {
      return NextResponse.json(
        { error: 'Failed to generate world' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({"success": true}, { status: 201 });
  } catch (error) {
    console.error('Error creating world:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
