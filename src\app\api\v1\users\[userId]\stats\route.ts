import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';

/**
 * GET /api/v1/users/[userId]/stats
 * Also handles /api/v1/users/me/stats when [userId] is 'me'
 * Get statistics for a user - counts of worlds, characters, and messages
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const { userId } = await params;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Check if the user exists
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('id', targetUserId)
      .single();

    if (userError) {
      if (userError.code === 'PGRST116') {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }

    // Get world count
    const { count: worldsCount, error: worldsError } = await supabaseAdmin
      .from('worlds')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', targetUserId);

    if (worldsError) {
      return NextResponse.json({ error: worldsError.message }, { status: 500 });
    }

    // Get character count
    const { count: charactersCount, error: charactersError } = await supabaseAdmin
      .from('characters')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', targetUserId);

    if (charactersError) {
      return NextResponse.json({ error: charactersError.message }, { status: 500 });
    }

    // Get message count
    const { count: messagesCount, error: messagesError } = await supabaseAdmin
      .from('messages')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', targetUserId);

    if (messagesError) {
      return NextResponse.json({ error: messagesError.message }, { status: 500 });
    }
    // Get user created date
    const { data: createdAt, error: createdAtError } = await supabaseAdmin
      .from('users')
      .select('created_at')
      .eq('id', targetUserId)
      .single();
    console.log(createdAt);
    if (createdAtError) {
      return NextResponse.json({ error: createdAtError.message }, { status: 500 });
    }

    // Return the statistics
    return NextResponse.json({
      worlds_count: worldsCount,
      characters_count: charactersCount,
      messages_count: messagesCount,
      user_id: targetUserId,
      created_at: createdAt['created_at'],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
