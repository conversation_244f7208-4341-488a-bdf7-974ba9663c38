import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { supabaseAdmin } from '@/lib/supabase-admin';

/**
 * GET /api/v1/users
 * List all users (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow authenticated users
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // admin check here
    const { data: userData, error: userError } = await supabaseAdmin
    .from('users')
    .select('is_admin')
    .eq('id', session.user.id)
    .single();

    if (userError || !userData?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }


    // Get all users from Supabase using admin client to bypass RLS
    const { data, error, count } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact' });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Return user data with sensitive information redacted
    const sanitizedUsers = data?.map(user => ({
      id: user.id,
      email: user.email.replace(/(?<=.{3}).(?=.*@)/g, '*'), // Redact part of the email
      name: user.name,
      created_at: user.created_at,
      // Don't include other sensitive fields
    }));

    return NextResponse.json({
      users: sanitizedUsers,
      count,
      current_user_id: session.user.id,
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/users
 * Create a new user (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow authenticated users
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Admin check
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('users')
      .select('is_admin')
      .eq('id', session.user.id)
      .single();

    if (adminError || !adminData?.is_admin) {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
    }

    // Parse request body
    const newUserData = await request.json();

    // Validate required fields
    if (!newUserData.email || !newUserData.name) {
      return NextResponse.json(
        { error: 'Missing required fields: email and name are required' },
        { status: 400 }
      );
    }

    // Check if user with this email already exists
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('email', newUserData.email)
      .maybeSingle();

    if (checkError) {
      return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Create the user in Supabase
    const { data: newUser, error: createError } = await supabaseAdmin
      .from('users')
      .insert({
        email: newUserData.email,
        name: newUserData.name,
        subscription_tier: 'free', // Default to free tier
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      return NextResponse.json({ error: createError.message }, { status: 500 });
    }

    // Return the new user with sensitive information redacted
    return NextResponse.json({
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
      subscription_tier: newUser.subscription_tier,
      created_at: newUser.created_at,
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}