/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '**',
      },
    ],
  },
  webpack: (config, { isDev, webpack }) => {
    if (!isDev) {
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /dev-tools/, // Exclude any module request containing "dev-tools"
        })
      );
    }
    return config;
  },
};

module.exports = nextConfig