import useSWR from 'swr'
import { World } from '@/types/world.types'
import { isWorldGenerating } from '@/utils/world-helpers'
import { useSession } from 'next-auth/react'

export function useUserWorlds() {
  const { status } = useSession()

  const {
    data: worlds,
    error,
    mutate,
  } = useSWR<World[]>(
    status === 'authenticated' ? `/api/v1/users/me/worlds` : null,
    async (url) => {
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch worlds')
      const data = await response.json()
      return Array.isArray(data) ? data :
             Array.isArray(data.worlds) ? data.worlds :
             data.worlds ? [data.worlds] : []
    },
    {
      refreshInterval: (data) =>
        data?.some((world) => isWorldGenerating(world)) ? 3000 : 0,
      revalidateOnFocus: false,
      dedupingInterval: 1000,
    }
  )

  return {
    worlds: worlds || [],
    isLoading: !worlds && !error,
    error,
    mutate,
  }
}
