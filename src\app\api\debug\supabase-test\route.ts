import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-admin';
import { v4 as uuidv4 } from 'uuid';

/**
 * Combined Supabase connectivity and permission test.
 * Optional query param `mode=admin|client` (default: admin)
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const mode = url.searchParams.get('mode') || 'admin';

  try {
    // Test connection
    const { data: connectionTest, error: connectionError } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: false });

    if (connectionError) {
      return NextResponse.json({
        success: false,
        message: 'Failed to connect to Supabase',
        error: connectionError.message,
        details: connectionError,
      }, { status: 500 });
    }

    // Insert test user
    const testUserId = uuidv4();
    const testEmail = `test-${Date.now()}@example.com`;

    const { data: insertData, error: insertError } = await supabaseAdmin
      .from('users')
      .insert({
        id: testUserId,
        email: testEmail,
        name: 'Test User',
        avatar_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select();

    if (insertError) {
      return NextResponse.json({
        success: false,
        message: 'Failed to insert test user',
        error: insertError.message,
        details: insertError,
      }, { status: 500 });
    }

    // Verify insert
    const { data: checkData, error: checkError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', testUserId)
      .single();

    if (checkError) {
      return NextResponse.json({
        success: false,
        message: 'Failed to verify inserted user',
        error: checkError.message,
        details: checkError,
      }, { status: 500 });
    }

    // Clean up
    const { error: deleteError } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', testUserId);

    return NextResponse.json({
      success: true,
      message: 'Supabase connection and user operations successful',
      connectionTest,
      insertData,
      checkData,
      cleanupSuccess: !deleteError,
    });
  } catch (error: any) {
    console.error('Unexpected error in Supabase test:', error);
    return NextResponse.json({
      success: false,
      message: 'Unexpected error during Supabase test',
      error: error.message || 'Unknown error',
    }, { status: 500 });
  }
}
