'use client'

import React from 'react'
import ReactMarkdown from 'react-markdown'
import { World } from '@/types/world.types'


type WorldInfoOverlayProps = {
  world: World
  onClose: () => void
}

export default function WorldInfoOverlay({ world, onClose }: WorldInfoOverlayProps) {
  // Close when clicking outside the modal
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-background-darker border border-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-medieval text-primary">{world.name}</h2>
              <div className="flex space-x-2 mt-2">
                {world.theme && (
                  <span className="px-2 py-1 bg-primary bg-opacity-20 border border-primary border-opacity-30 rounded-full text-xs">
                    {world.theme}
                  </span>
                )}
                {world.dmType && world.dmType !== 'normal' && (
                  <span className="px-2 py-1 bg-secondary bg-opacity-20 border border-secondary border-opacity-30 rounded-full text-xs">
                    {`${world.dmType.charAt(0).toUpperCase()}${world.dmType.slice(1)} DM`}
                  </span>
                )}
              </div>
            </div>
            <button 
              onClick={onClose}
              className="text-text-secondary hover:text-white transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* World Details */}
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-medieval text-primary mb-3">Description</h3>
              <div className="text-text-secondary prose prose-invert prose-sm max-w-none">
                <ReactMarkdown>{JSON.stringify(world.description ?? {})}</ReactMarkdown>
              </div>
            </div>

            {world.notes && (
              <div>
                <h3 className="text-lg font-medieval text-primary mb-3">Notes</h3>
                <div className="text-text-secondary prose prose-invert prose-sm max-w-none">
                  <ReactMarkdown>{world.notes}</ReactMarkdown>
                </div>
              </div>
            )}

            {world.settings && Object.keys(world.settings).length > 0 && (
              <div>
                <h3 className="text-lg font-medieval text-primary mb-3">Settings</h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(world.settings).map(([key, value]) => (
                    <div key={key} className="bg-background p-3 rounded border border-gray-800">
                      <span className="text-text-secondary text-sm capitalize">{key.replace(/_/g, ' ')}</span>
                      <div className="text-lg font-bold">{String(value)}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 