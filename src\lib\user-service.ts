import { supabaseAdmin } from './supabase-admin';
import { User } from 'next-auth';
import { v4 as uuidv4 } from 'uuid';
import { SubscriptionTier } from '@/types/schema.types';

/**
 * Creates or updates a user in the Supabase database
 */
export async function upsertUser(user: User) {
  try {
    // If we're creating a Google user with a string ID (google-email@format),
    // we need to convert it to a UUID for database compliance
    let userId = user.id;

    // Check if the ID is already a valid UUID
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)) {
      // Check if this user already exists in the database by email
      const existingUser = await getUserByEmail(user.email as string);

      if (existingUser) {
        // If user exists, use their UUID
        userId = existingUser.id;
      } else {
        // Generate a new UUID for this user
        userId = uuidv4();
      }
    }

    const { data, error } = await supabaseAdmin
      .from('users')
      .upsert({
        id: userId,
        email: user.email as string,
        name: user.name,
        avatar_url: user.image,
        subscription_tier: 'free', // Default to free tier for new users
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'id',
        ignoreDuplicates: false,
      })
      .select();

    if (error) {
      console.error('Error upserting user:', error);
      return null;
    }

    return data?.[0] || null;
  } catch (error) {
    console.error('Error in upsertUser:', error);
    return null;
  }
}

/**
 * Gets a user by their ID
 */
export async function getUserById(id: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getUserById:', error);
    return null;
  }
}

/**
 * Gets a user by their email
 */
export async function getUserByEmail(email: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 is the error for "no rows returned" - we don't need to log this
      console.error('Error getting user by email:', error);
    }

    return data || null;
  } catch (error) {
    console.error('Error in getUserByEmail:', error);
    return null;
  }
}

/**
 * Updates a user's subscription tier
 */
export async function updateUserSubscriptionTier(userId: string, subscriptionTier: SubscriptionTier) {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update({
        subscription_tier: subscriptionTier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user subscription tier:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in updateUserSubscriptionTier:', error);
    return null;
  }
}

/**
 * Gets user statistics (counts of worlds, characters, campaigns)
 */
export async function getUserStats(userId: string) {
  try {
    // Get world count
    const { count: worldCount, error: worldError } = await supabaseAdmin
      .from('worlds')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (worldError) {
      console.error('Error getting world count:', worldError);
    }

    // Get character count
    const { count: characterCount, error: characterError } = await supabaseAdmin
      .from('characters')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (characterError) {
      console.error('Error getting character count:', characterError);
    }

    // Get campaigns where user is DM
    const { count: dmCampaignCount, error: dmCampaignError } = await supabaseAdmin
      .from('campaigns')
      .select('id', { count: 'exact', head: true })
      .eq('dm_user_id', userId);

    if (dmCampaignError) {
      console.error('Error getting DM campaign count:', dmCampaignError);
    }

    // Get campaigns where user is a participant
    const { count: playerCampaignCount, error: playerCampaignError } = await supabaseAdmin
      .from('campaign_participants')
      .select('campaign_id', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (playerCampaignError) {
      console.error('Error getting player campaign count:', playerCampaignError);
    }

    // Get message count
    const { count: messageCount, error: messageError } = await supabaseAdmin
      .from('messages')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (messageError) {
      console.error('Error getting message count:', messageError);
    }

    return {
      worlds: worldCount || 0,
      characters: characterCount || 0,
      campaigns: (dmCampaignCount || 0) + (playerCampaignCount || 0),
      messages: messageCount || 0
    };
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return {
      worlds: 0,
      characters: 0,
      campaigns: 0,
      messages: 0
    };
  }
}