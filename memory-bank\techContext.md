# Technical Context: SoloQuest

## Core Technologies

*   **Framework:** Next.js v15
*   **UI Library:** React
*   **Language:** TypeScript
*   **Database:** Supabase (PostgreSQL)
*   **Authentication:** NextAuth.js
*   **Styling:** Tailwind CSS
*   **Runtime:** Node.js

## Development Setup

*   **Package Manager:** npm (inferred from `package-lock.json`)
*   **Environment:** Requires Node.js and npm installed.
*   **Configuration:**
    *   Next.js config: `next.config.js`
    *   TypeScript config: `tsconfig.json`
    *   PostCSS config (for Tailwind): `postcss.config.js`
    *   ESLint config: `.eslintrc.json`
*   **Environment Variables:** Centralized access via `src/lib/env.ts`, which reads `process.env` (populated from `.env` files). Correctly uses `NEXT_PUBLIC_` prefix for client-side vars. Includes a `validate()` method for runtime checks of critical variables (used in `layout.tsx`). Requires Supabase keys, NextAuth secret, Google OAuth keys, etc.
*   **Database Setup:** Supabase project required. Schema managed via `supabase/schema.sql`. Migrations is handled manually.

## Key Dependencies (Inferred from file structure & common practices)

*   `next`: Core framework
*   `react`, `react-dom`: UI library
*   `typescript`: Language support
*   `@supabase/supabase-js`: Supabase client library. Used to initialize clients.
    *   An admin client (`supabaseAdmin`) is created in `lib/supabase-admin.ts` using the service role key, bypassing RLS for server-side operations. It uses generated DB types (`Database` from `lib/database.types.ts`).
    *   A separate client instance for client-side/RLS-respecting operations likely exists or needs to be created.
*   `next-auth`: Authentication library (backend setup)
*   `next-auth/react`: Client-side hooks (`useSession`, `signOut`) for session management in React components.
*   `tailwindcss`, `postcss`, `autoprefixer`: Styling (Confirmed). Used as a base, extended with custom classes and CSS variables in `globals.css`.
*   `eslint`: Linting
*   `clsx`: Utility for conditionally joining class names (seen in `layout.tsx`)
*   `next/font`: Font optimization (Inter font used in `layout.tsx`)
*   `framer-motion`: UI animation library (seen in `page.tsx`)
*   `uuid`: Library for generating UUIDs (used in `user-service.ts`).
*   (Potentially AI-related libraries: TBD)
*   (Potentially state management libraries like `swr` or `@tanstack/react-query`: TBD)

## Technical Constraints

*   Web-based application, runs in a browser.
*   Relies on external services: Supabase for database and potentially auth.
*   Requires internet connectivity to function fully.
*   Performance considerations related to SSR/CSR and database queries.

## Tool Usage Patterns

*   `yarn dev`: Start development server (standard Next.js script).
*   `yarn build`: Create production build.
*   `yarn start`: Run production server.
*   `npm run lint`: Run ESLint checks.
*   Client-side session management using `useSession` hook from `next-auth/react` is a key pattern (`Header.tsx`).
*   Client-side Data Fetching: Client components use helper functions (e.g., `fetchWorlds` in `world-service.ts`) that call internal Next.js API routes via `fetch`. This abstracts direct database/service calls from the client.
*   Server-side API Authorization: Helper functions in `lib/auth-helpers.ts` secure API routes using `getServerSession` and `supabaseAdmin`.
*   Service Layer Pattern: Database logic is abstracted into service files (e.g., `lib/user-service.ts`, `lib/world-service.ts`) which typically use the `supabaseAdmin` client. Services may also transform data for the frontend.
*   External AI Service Integration: Complex AI tasks (e.g., world generation in `world-service.ts`) are delegated to an external service via HTTP requests (`fetch`) to an `INTERNAL_API_URL`.
*   Theming: A specific fantasy theme is implemented using custom fonts, CSS variables, and custom utility classes in `globals.css`.
*   Database schema changes likely require manual updates to `supabase/schema.sql` and potentially applying them via Supabase dashboard or CLI.
