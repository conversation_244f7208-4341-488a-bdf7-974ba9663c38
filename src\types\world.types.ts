/**
 * World-related type definitions
 */

export type DMType = 'normal' | 'hardcore' | 'funny' | 'easygoing';

export type WorldStatus = 'generating' | 'completed';

/**
 * World entity as returned from the API
 */
export type World = {
  id: string;
  name: string;
  theme?: string;
  dmType?: DMType;
  createdAt?: string;
  created_at?: string; // Alternative field name from the API
  updated_at?: string;
  status?: WorldStatus;
  description?: Record<string, any>;
  user_id?: string;
  notes?: string;
  settings?: Record<string, any>;
};

/**
 * Form for creating a new world
 */
export type WorldCreationForm = {
  name: string;
  theme: string;
  worldCustomization: string;
  dmType: DMType;
  playerCharacterCustomization: string;
  aiCharacterCount: number;
  characterCustomizations: string[];
};

/**
 * Campaign overview data structure found in the world description
 */
export type CampaignOverview = {
  setting?: string;
  central_conflict?: string;
  character_integration?: string;
};

/**
 * Parsed world description structure
 */
export type WorldDescription = {
  world_name?: string;
  campaign_overview?: CampaignOverview;
  // Add other fields as needed
  [key: string]: any;
}; 